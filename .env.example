APP_NAME="BeaconLMS"
APP_ENV=local
APP_KEY=base64:+i90jrMPXPMwb3XX5Iv1yy54gPe+PD7j+bHcCi5JNDI=
APP_DEBUG=true
APP_URL=http://localhost

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

# Database Configuration for BeaconLMS
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=beaconlms
DB_USERNAME=root
DB_PASSWORD=

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=database
# CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_SCHEME=null
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

# Moodle Integration Configuration
MOODLE_URL=http://localhost/webservice/rest/server.php
MOODLE_TOKEN=b1d7c243c9d7d61018f7dcebc8d9ccd
MOODLE_SERVICE=moodle_mobile_app
MOODLE_TIMEOUT=30

# BeaconLMS SSO Configuration
BEACON_SSO_SECRET=beacon_lms

# BeaconLMS Specific Configuration
CTP_COURSE_ID=1
CTP_EXAM_PREPARATION_DAYS=43
XP_VIDEO_COMPLETION=20
XP_QUIZ_PASSED=50
XP_ASSIGNMENT_SUBMITTED=40
XP_CHAPTER_COMPLETION=100
XP_CERTIFICATION_REQUIRED=1500

# Engagement Settings
MINIMUM_DAILY_ACTIVITY_MINUTES=30
WEEKEND_STREAK_MULTIPLIER=2
PROGRESS_CACHE_TTL=300

# File Upload Settings
MAX_PROFILE_PHOTO_SIZE=2048
ALLOWED_PHOTO_EXTENSIONS=jpg,jpeg,png,gif
