<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\CacheManager;
use App\Models\User;
use App\Models\UserSession;

class CacheWarmCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'cache:warm {--user= : Warm cache for specific user ID} {--all : Warm cache for all active users}';

    /**
     * The console command description.
     */
    protected $description = 'Warm the cache with frequently accessed data for better performance';

    /**
     * Execute the console command.
     */
    public function handle(CacheManager $cacheManager): int
    {
        $this->info('🔥 Starting cache warming process...');

        if ($this->option('user')) {
            $userId = $this->option('user');
            $user = User::find($userId);

            if (!$user) {
                $this->error("User with ID {$userId} not found.");
                return Command::FAILURE;
            }

            $this->warmUserCache($cacheManager, $user);
            $this->info("✅ Cache warmed for user: {$user->name} (ID: {$userId})");

        } elseif ($this->option('all')) {
            $this->warmAllActiveUsersCache($cacheManager);
            $this->info("✅ Cache warmed for all active users");

        } else {
            $this->warmPopularCache($cacheManager);
            $this->info("✅ Popular cache data warmed");
        }

        $this->info('🎉 Cache warming completed successfully!');
        return Command::SUCCESS;
    }

    /**
     * Warm cache for a specific user
     */
    private function warmUserCache(CacheManager $cacheManager, User $user): void
    {
        $this->info("Warming cache for user: {$user->name}");

        $bar = $this->output->createProgressBar(5);
        $bar->start();

        // Warm dashboard stats cache
        $cacheManager->remember(
            key: "user_{$user->id}_dashboard_stats",
            ttl: 300,
            tags: ['user_stats', "user_{$user->id}"],
            callback: fn() => $this->getDashboardStats($user),
        );
        $bar->advance();

        // Warm course progress cache
        $enrollments = $user->courseEnrollments()->with('course')->get();
        foreach ($enrollments as $enrollment) {
            $cacheManager->remember(
                key: "user_{$user->id}_course_{$enrollment->course_id}_progress",
                ttl: 300,
                tags: ['progress', "user_{$user->id}", "course_{$enrollment->course_id}"],
                callback: fn() => $enrollment->progress,
                activityBased: true
            );
        }
        $bar->advance();

        // Warm user profile data cache
        $cacheManager->remember(
            key: "user_{$user->id}_profile",
            ttl: 600,
            tags: ['user_profile', "user_{$user->id}"],
            callback: fn() => $this->getUserProfileData($user),
        );
        $bar->advance();

        // Warm recent activity cache
        $cacheManager->remember(
            key: "user_{$user->id}_recent_activity",
            ttl: 180,
            tags: ['activity', "user_{$user->id}"],
            callback: fn() => $this->getRecentActivity($user),
        );
        $bar->advance();

        // Warm upcoming exams cache
        $cacheManager->remember(
            key: "user_{$user->id}_upcoming_exams",
            ttl: 3600,
            tags: ['exams', "user_{$user->id}"],
            callback: fn() => $this->getUpcomingExams($user),
        );
        $bar->advance();

        $bar->finish();
        $this->newLine();
    }

    /**
     * Warm cache for all active users
     */
    private function warmAllActiveUsersCache(CacheManager $cacheManager): void
    {
        $activeUsers = UserSession::getActiveUsers();

        if ($activeUsers->isEmpty()) {
            $this->info("No active users found for cache warming.");
            return;
        }

        $this->info("Found {$activeUsers->count()} active users for cache warming");

        $bar = $this->output->createProgressBar($activeUsers->count());
        $bar->start();

        foreach ($activeUsers as $user) {
            $this->warmUserCache($cacheManager, $user);
            $bar->advance();
        }

        $bar->finish();
        $this->newLine();
    }

    /**
     * Warm popular cache data (global cache)
     */
    private function warmPopularCache(CacheManager $cacheManager): void
    {
        $this->info("Warming popular cache data...");

        $bar = $this->output->createProgressBar(3);
        $bar->start();

        // Warm course list cache
        $cacheManager->remember(
            key: "popular_courses",
            ttl: 1800,
            tags: ['courses', 'popular'],
            callback: fn() => $this->getPopularCourses(),
        );
        $bar->advance();

        // Warm leaderboard cache
        $cacheManager->remember(
            key: "leaderboard_top_10",
            ttl: 600,
            tags: ['leaderboard', 'stats'],
            callback: fn() => $this->getLeaderboardData(),
        );
        $bar->advance();

        // Warm system stats cache
        $cacheManager->remember(
            key: "system_stats",
            ttl: 3600,
            tags: ['system', 'stats'],
            callback: fn() => $this->getSystemStats(),
        );
        $bar->advance();

        $bar->finish();
        $this->newLine();
    }

    /**
     * Get dashboard statistics for user
     */
    private function getDashboardStats(User $user): array
    {
        return [
            'total_xp' => $user->getTotalXP() ?? 0,
            'current_streak' => $user->current_streak ?? 0,
            'total_study_hours' => $user->getStudyHours() ?? 0,
            'courses_completed' => $user->courseEnrollments()->where('progress', 100)->count(),
            'average_progress' => round($user->courseEnrollments()->avg('progress') ?? 0, 1),
        ];
    }

    /**
     * Get user profile data
     */
    private function getUserProfileData(User $user): array
    {
        $enrollments = $user->courseEnrollments()->with('course')->get();

        return [
            'total_courses' => $enrollments->count(),
            'completed_courses' => $enrollments->where('progress', 100)->count(),
            'average_progress' => round($enrollments->avg('progress') ?? 0, 1),
            'total_activities' => $enrollments->sum(function ($enrollment) {
                return $enrollment->course->chapters->sum(function ($chapter) {
                    return $chapter->items->count();
                });
            }),
            'completed_activities' => $enrollments->sum(function ($enrollment) {
                return $enrollment->course->chapters->sum(function ($chapter) {
                    return $chapter->items->where('is_completed', true)->count();
                });
            }),
        ];
    }

    /**
     * Get recent activity for user
     */
    private function getRecentActivity(User $user): array
    {
        return $user->engagementLogs()
            ->with('course')
            ->latest()
            ->take(10)
            ->get()
            ->map(function ($log) {
                return [
                    'course_name' => $log->course->title ?? 'Unknown Course',
                    'activity_type' => $log->activity_type ?? 'study',
                    'study_hours' => $log->study_hours,
                    'date' => $log->date->format('Y-m-d'),
                ];
            })
            ->toArray();
    }

    /**
     * Get upcoming exams for user
     */
    private function getUpcomingExams(User $user): array
    {
        return $user->courseEnrollments()
            ->whereNotNull('exam_date')
            ->where('exam_date', '>', now())
            ->with('course')
            ->orderBy('exam_date')
            ->take(5)
            ->get()
            ->map(function ($enrollment) {
                return [
                    'course_name' => $enrollment->course->title,
                    'exam_date' => $enrollment->exam_date->format('Y-m-d'),
                    'days_remaining' => now()->diffInDays($enrollment->exam_date),
                ];
            })
            ->toArray();
    }

    /**
     * Get popular courses
     */
    private function getPopularCourses(): array
    {
        return \App\Models\CourseEnrollment::select('course_id', \DB::raw('count(*) as enrollment_count'))
            ->with('course')
            ->groupBy('course_id')
            ->orderByDesc('enrollment_count')
            ->take(10)
            ->get()
            ->map(function ($enrollment) {
                return [
                    'id' => $enrollment->course->id,
                    'title' => $enrollment->course->title,
                    'enrollment_count' => $enrollment->enrollment_count,
                ];
            })
            ->toArray();
    }

    /**
     * Get leaderboard data
     */
    private function getLeaderboardData(): array
    {
        return User::select('id', 'name', 'email')
            ->withTotalXP()
            ->orderByDesc('total_xp')
            ->take(10)
            ->get()
            ->map(function ($user, $index) {
                return [
                    'rank' => $index + 1,
                    'name' => $user->name,
                    'total_xp' => $user->total_xp ?? 0,
                ];
            })
            ->toArray();
    }

    /**
     * Get system statistics
     */
    private function getSystemStats(): array
    {
        return [
            'total_users' => User::count(),
            'total_courses' => \App\Models\Course::count(),
            'total_enrollments' => \App\Models\CourseEnrollment::count(),
            'active_users_today' => UserSession::getActiveUsersCount(),
            'avg_study_hours_per_user' => round(User::avgStudyHours() ?? 0, 2),
        ];
    }
}
