<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use App\Models\User;
use App\Events\DataUpdatedEvent;
use App\Listeners\CacheInvalidationListener;
use App\Services\CacheManager;

class TestCacheListenerCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:cache-listener {--user-id= : Test with specific user ID} {--event-type=progress_updated : Event type to test} {--no-cache : Skip cache testing}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the cache invalidation listener to ensure it\'s working properly';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🔍 Testing Cache Invalidation Listener...');
        $this->newLine();

        // Step 1: Test Listener Registration
        $this->testListenerRegistration();

        // Step 2: Create or get test user
        $user = $this->getTestUser();

        // Step 3: Test Event Firing and Listener Response
        $this->testEventHandling($user);

        // Step 4: Test Cache Manager Integration
        if (!$this->option('no-cache')) {
            $this->testCacheIntegration($user);
        }

        $this->newLine();
        $this->info('✅ All tests completed successfully!');
        $this->info('🎯 Your CacheInvalidationListener is working properly.');

        return Command::SUCCESS;
    }

    /**
     * Test that the listener is properly registered
     */
    private function testListenerRegistration(): void
    {
        $this->info('📋 Step 1: Checking Listener Registration...');

        // Check if the listener class exists
        if (!class_exists(CacheInvalidationListener::class)) {
            $this->error('❌ CacheInvalidationListener class does not exist!');
            return;
        }

        // Check if listener is registered in AppServiceProvider
        $appServiceProvider = app(AppServiceProvider::class);
        $reflection = new \ReflectionClass($appServiceProvider);
        $bootMethod = $reflection->getMethod('boot');
        $bootContent = file_get_contents($reflection->getFileName());

        if (str_contains($bootContent, 'DataUpdatedEvent::class, CacheInvalidationListener::class')) {
            $this->info('✅ CacheInvalidationListener is registered in AppServiceProvider');
        } else {
            $this->error('❌ CacheInvalidationListener is NOT registered in AppServiceProvider!');
            $this->info('🔧 Add this to app/Providers/AppServiceProvider.php boot() method:');
            $this->info('   Event::listen(DataUpdatedEvent::class, CacheInvalidationListener::class);');
            return;
        }

        // Check if listener implements ShouldQueue (queued listener)
        $listenerReflection = new \ReflectionClass(CacheInvalidationListener::class);
        $implementsShouldQueue = $listenerReflection->implementsInterface(\Illuminate\Contracts\Queue\ShouldQueue::class);

        if ($implementsShouldQueue) {
            $this->info('✅ CacheInvalidationListener is configured as a queued listener');
            $this->info('ℹ️  Queued listeners are not returned by Event::getListeners()');
        } else {
            $this->info('✅ CacheInvalidationListener is configured as a synchronous listener');
        }

        // Test dependency injection
        try {
            $listener = app(CacheInvalidationListener::class);
            $this->info('✅ CacheInvalidationListener dependency injection works');
        } catch (\Exception $e) {
            $this->error('❌ CacheInvalidationListener dependency injection failed: ' . $e->getMessage());
        }
    }

    /**
     * Get or create a test user
     */
    private function getTestUser(): User
    {
        $this->info('👤 Step 2: Setting up test user...');

        $userId = $this->option('user-id');

        if ($userId) {
            $user = User::find($userId);
            if (!$user) {
                $this->error("❌ User with ID {$userId} not found!");
                exit(1);
            }
        } else {
            $user = User::where('email', '<EMAIL>')->first();
            if (!$user) {
                $user = User::create([
                    'name' => 'Test Cache User',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('password'),
                    'role' => 'student',
                    'last_activity_date' => now()->subDays(1),
                    'current_streak' => 1
                ]);
                $this->info('✅ Created new test user: ' . $user->name);
            } else {
                $this->info('✅ Using existing test user: ' . $user->name);
            }
        }

        return $user;
    }

    /**
     * Test event firing and listener handling
     */
    private function testEventHandling(User $user): void
    {
        $this->info('🔥 Step 3: Testing Event Handling...');

        $eventType = $this->option('event-type') ?? 'progress_updated';
        $testData = [
            'course_id' => 1,
            'progress' => 75,
            'test_timestamp' => now()->toISOString()
        ];

        $this->info("📡 Firing DataUpdatedEvent: {$eventType}");
        $this->info('📊 Event data: ' . json_encode($testData));

        try {
            // Clear any existing logs for clean testing
            Log::info('TestCacheListener: Event fired', [
                'event_type' => $eventType,
                'user_id' => $user->id,
                'data' => $testData
            ]);

            // Fire the event
            event(new DataUpdatedEvent(
                eventType: $eventType,
                user: $user,
                data: $testData,
                affectedCacheTypes: ['stats', 'progress']
            ));

            $this->info('✅ DataUpdatedEvent fired successfully');
            $this->info('📝 Check your logs for CacheInvalidationListener activity');

        } catch (\Exception $e) {
            $this->error('❌ Failed to fire event: ' . $e->getMessage());
            Log::error('TestCacheListener: Event firing failed', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Test cache manager integration
     */
    private function testCacheIntegration(User $user): void
    {
        $this->info('💾 Step 4: Testing Cache Integration...');

        try {
            $cacheManager = app(CacheManager::class);

            // Create test cache data
            $cacheKey = "test_user_{$user->id}_data";
            $testData = ['cached' => 'value', 'timestamp' => now()->toISOString()];

            $cachedResult = $cacheManager->remember(
                key: $cacheKey,
                ttl: 300,
                tags: ['test', "user_{$user->id}"],
                callback: fn() => $testData
            );

            $this->info('✅ Cache data created successfully');

            // Fire event to trigger cache invalidation
            $this->info('🔄 Firing event to test cache invalidation...');
            event(new DataUpdatedEvent(
                eventType: 'test_cache_invalidation',
                user: $user,
                data: ['test' => 'cache_invalidation'],
                affectedCacheTypes: ['test']
            ));

            $this->info('✅ Cache invalidation test completed');
            $this->info('📝 Check cache_statistics table for performance metrics');

        } catch (\Exception $e) {
            $this->error('❌ Cache integration test failed: ' . $e->getMessage());
            Log::error('TestCacheListener: Cache test failed', ['error' => $e->getMessage()]);
        }
    }
}
