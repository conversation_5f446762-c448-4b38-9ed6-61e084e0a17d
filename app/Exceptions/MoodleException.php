<?php

namespace App\Exceptions;

use Exception;

class MoodleException extends Exception
{
    protected string $moodleErrorCode;
    protected array $moodleData;

    public function __construct(
        string $message = '',
        string $moodleErrorCode = '',
        array $moodleData = [],
        int $code = 0,
        \Throwable $previous = null
    ) {
        parent::__construct($message, $code, $previous);
        
        $this->moodleErrorCode = $moodleErrorCode;
        $this->moodleData = $moodleData;
    }

    public function getMoodleErrorCode(): string
    {
        return $this->moodleErrorCode;
    }

    public function getMoodleData(): array
    {
        return $this->moodleData;
    }

    public function getContext(): array
    {
        return [
            'message' => $this->getMessage(),
            'moodle_error_code' => $this->moodleErrorCode,
            'moodle_data' => $this->moodleData,
            'file' => $this->getFile(),
            'line' => $this->getLine(),
        ];
    }
}
