<?php

namespace App\Filament\Pages;

use App\Models\User;
use App\Models\Course;
use App\Models\CourseEnrollment;
use App\Models\UserEngagementLog;
use Filament\Pages\Dashboard as BaseDashboard;
use Filament\Widgets\StatsOverviewWidget as BaseStatsOverviewWidget;
use Filament\Widgets\ChartWidget;

class Dashboard extends BaseDashboard
{
    public function getWidgets(): array
    {
        return [
            \App\Filament\Widgets\StatsOverview::class,
            \App\Filament\Widgets\EnrollmentChart::class,
            \App\Filament\Widgets\ProgressChart::class,
            \App\Filament\Widgets\RecentActivity::class,
        ];
    }
}
