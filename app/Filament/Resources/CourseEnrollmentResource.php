<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CourseEnrollmentResource\Pages;
use App\Models\CourseEnrollment;
use App\Models\User;
use App\Models\Course;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Illuminate\Database\Eloquent\Builder;

class CourseEnrollmentResource extends Resource
{
    protected static ?string $model = CourseEnrollment::class;

    protected static ?string $navigationIcon = 'heroicon-o-clipboard-document-list';

    protected static ?string $navigationGroup = 'Course Management';

    protected static ?int $navigationSort = 2;

    protected static ?string $navigationLabel = 'Enrollments';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Enrollment Information')
                    ->schema([
                        Forms\Components\Select::make('user_id')
                            ->label('Student')
                            ->options(User::where('role', 'student')->pluck('name', 'id'))
                            ->searchable()
                            ->required(),
                        Forms\Components\Select::make('course_id')
                            ->label('Course')
                            ->options(Course::pluck('title', 'id'))
                            ->searchable()
                            ->required(),
                        Forms\Components\TextInput::make('moodle_enrollment_id')
                            ->label('Moodle Enrollment ID')
                            ->numeric()
                            ->placeholder('Auto-generated when synced'),
                    ])->columns(3),

                Forms\Components\Section::make('Progress & Dates')
                    ->schema([
                        Forms\Components\TextInput::make('progress')
                            ->label('Progress (%)')
                            ->numeric()
                            ->minValue(0)
                            ->maxValue(100)
                            ->default(0)
                            ->suffix('%'),
                        Forms\Components\DateTimePicker::make('enrolled_at')
                            ->required()
                            ->default(now()),
                        Forms\Components\DateTimePicker::make('completed_at')
                            ->placeholder('Not completed yet'),
                        Forms\Components\DatePicker::make('exam_date')
                            ->required()
                            ->default(now()->addDays(config('beaconlms.ctp_course.exam_preparation_days'))),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('user.name')
                    ->label('Student')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('course.title')
                    ->label('Course')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('progress')
                    ->label('Progress')
                    ->sortable()
                    ->suffix('%')
                    ->color(fn (string $state): string => match (true) {
                        $state >= 80 => 'success',
                        $state >= 50 => 'warning',
                        default => 'danger',
                    }),
                Tables\Columns\TextColumn::make('enrolled_at')
                    ->label('Enrolled')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('exam_date')
                    ->label('Exam Date')
                    ->date()
                    ->sortable()
                    ->color(fn ($record): string => 
                        $record->exam_date && $record->exam_date->isPast() ? 'danger' : 'primary'
                    ),
                Tables\Columns\TextColumn::make('days_until_exam')
                    ->label('Days to Exam')
                    ->sortable()
                    ->color(fn (string $state): string => match (true) {
                        $state < 0 => 'danger',
                        $state <= 7 => 'warning',
                        default => 'success',
                    }),
                Tables\Columns\IconColumn::make('is_completed')
                    ->label('Completed')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-clock')
                    ->trueColor('success')
                    ->falseColor('warning'),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('course_id')
                    ->label('Course')
                    ->options(Course::pluck('title', 'id')),
                Tables\Filters\Filter::make('completed')
                    ->query(fn (Builder $query): Builder => $query->whereNotNull('completed_at'))
                    ->label('Completed'),
                Tables\Filters\Filter::make('active')
                    ->query(fn (Builder $query): Builder => $query->whereNull('completed_at'))
                    ->label('Active'),
                Tables\Filters\Filter::make('upcoming_exams')
                    ->query(fn (Builder $query): Builder => $query->whereBetween('exam_date', [now(), now()->addDays(7)]))
                    ->label('Upcoming Exams (7 days)'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('sync_progress')
                    ->icon('heroicon-o-arrow-path')
                    ->color('info')
                    ->action(function (CourseEnrollment $record) {
                        \App\Jobs\SyncStudentProgress::dispatch($record->user);
                        \Filament\Notifications\Notification::make()
                            ->title('Progress sync initiated')
                            ->success()
                            ->send();
                    }),
                Tables\Actions\Action::make('mark_completed')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->action(function (CourseEnrollment $record) {
                        $record->markAsCompleted();
                        \Filament\Notifications\Notification::make()
                            ->title('Enrollment marked as completed')
                            ->success()
                            ->send();
                    })
                    ->visible(fn (CourseEnrollment $record) => !$record->is_completed),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('sync_progress')
                        ->label('Sync Progress')
                        ->icon('heroicon-o-arrow-path')
                        ->color('info')
                        ->action(function ($records) {
                            foreach ($records as $record) {
                                \App\Jobs\SyncStudentProgress::dispatch($record->user);
                            }
                            \Filament\Notifications\Notification::make()
                                ->title('Progress sync initiated for ' . count($records) . ' enrollments')
                                ->success()
                                ->send();
                        }),
                ]),
            ]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Enrollment Details')
                    ->schema([
                        Infolists\Components\TextEntry::make('user.name')
                            ->label('Student'),
                        Infolists\Components\TextEntry::make('course.title')
                            ->label('Course'),
                        Infolists\Components\TextEntry::make('progress')
                            ->suffix('%')
                            ->color(fn (string $state): string => match (true) {
                                $state >= 80 => 'success',
                                $state >= 50 => 'warning',
                                default => 'danger',
                            }),
                        Infolists\Components\TextEntry::make('moodle_enrollment_id')
                            ->label('Moodle ID')
                            ->placeholder('Not synced'),
                    ])->columns(4),

                Infolists\Components\Section::make('Important Dates')
                    ->schema([
                        Infolists\Components\TextEntry::make('enrolled_at')
                            ->dateTime(),
                        Infolists\Components\TextEntry::make('exam_date')
                            ->date(),
                        Infolists\Components\TextEntry::make('days_until_exam')
                            ->suffix(' days'),
                        Infolists\Components\TextEntry::make('completed_at')
                            ->dateTime()
                            ->placeholder('Not completed'),
                    ])->columns(4),

                Infolists\Components\Section::make('Progress Statistics')
                    ->schema([
                        Infolists\Components\TextEntry::make('getXPEarned')
                            ->label('XP Earned')
                            ->suffix(' XP'),
                        Infolists\Components\TextEntry::make('enrollment_duration')
                            ->label('Enrollment Duration')
                            ->suffix(' days'),
                        Infolists\Components\TextEntry::make('isEligibleForCertification')
                            ->label('Certification Eligible')
                            ->formatStateUsing(fn (bool $state): string => $state ? 'Yes' : 'No')
                            ->color(fn (bool $state): string => $state ? 'success' : 'warning'),
                    ])->columns(3),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCourseEnrollments::route('/'),
            'create' => Pages\CreateCourseEnrollment::route('/create'),
            'view' => Pages\ViewCourseEnrollment::route('/{record}'),
            'edit' => Pages\EditCourseEnrollment::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }
}
