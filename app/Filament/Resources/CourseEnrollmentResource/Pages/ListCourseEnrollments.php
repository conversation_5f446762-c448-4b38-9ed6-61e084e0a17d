<?php

namespace App\Filament\Resources\CourseEnrollmentResource\Pages;

use App\Filament\Resources\CourseEnrollmentResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;

class ListCourseEnrollments extends ListRecords
{
    protected static string $resource = CourseEnrollmentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    public function getTabs(): array
    {
        return [
            'all' => Tab::make('All Enrollments'),
            'active' => Tab::make('Active')
                ->modifyQueryUsing(fn (Builder $query) => $query->whereNull('completed_at')),
            'completed' => Tab::make('Completed')
                ->modifyQueryUsing(fn (Builder $query) => $query->whereNotNull('completed_at')),
            'upcoming_exams' => Tab::make('Upcoming Exams')
                ->modifyQueryUsing(fn (Builder $query) => $query->whereBetween('exam_date', [now(), now()->addDays(7)])),
            'overdue_exams' => Tab::make('Overdue Exams')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('exam_date', '<', now())->whereNull('completed_at')),
        ];
    }
}
