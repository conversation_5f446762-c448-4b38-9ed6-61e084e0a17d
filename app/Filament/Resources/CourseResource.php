<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CourseResource\Pages;
use App\Models\Course;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Infolists;
use Filament\Infolists\Infolist;

class CourseResource extends Resource
{
    protected static ?string $model = Course::class;

    protected static ?string $navigationIcon = 'heroicon-o-academic-cap';

    protected static ?string $navigationGroup = 'Course Management';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Course Information')
                    ->schema([
                        Forms\Components\TextInput::make('title')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\Textarea::make('description')
                            ->required()
                            ->rows(3),
                        Forms\Components\TextInput::make('moodle_course_id')
                            ->label('Moodle Course ID')
                            ->numeric()
                            ->required()
                            ->unique(ignoreRecord: true),
                    ]),

                Forms\Components\Section::make('Course Media')
                    ->schema([
                        Forms\Components\FileUpload::make('thumbnail')
                            ->image()
                            ->directory('course-thumbnails')
                            ->maxSize(2048),
                    ]),

                Forms\Components\Section::make('Course Statistics')
                    ->schema([
                        Forms\Components\TextInput::make('duration_days')
                            ->label('Duration (Days)')
                            ->numeric()
                            ->default(30)
                            ->minValue(1),
                        Forms\Components\TextInput::make('total_hours')
                            ->label('Total Hours')
                            ->numeric()
                            ->default(0)
                            ->minValue(0),
                        Forms\Components\TextInput::make('total_videos')
                            ->label('Total Videos')
                            ->numeric()
                            ->default(0)
                            ->minValue(0),
                        Forms\Components\TextInput::make('total_quizzes')
                            ->label('Total Quizzes')
                            ->numeric()
                            ->default(0)
                            ->minValue(0),
                    ])->columns(4),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('thumbnail')
                    ->square()
                    ->defaultImageUrl('https://via.placeholder.com/150x150/EBF4FF/7F9CF5?text=Course'),
                Tables\Columns\TextColumn::make('title')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),
                Tables\Columns\TextColumn::make('moodle_course_id')
                    ->label('Moodle ID')
                    ->sortable(),
                Tables\Columns\TextColumn::make('duration_days')
                    ->label('Duration')
                    ->suffix(' days')
                    ->sortable(),
                Tables\Columns\TextColumn::make('enrollments_count')
                    ->label('Students')
                    ->counts('enrollments')
                    ->sortable(),
                Tables\Columns\TextColumn::make('chapters_count')
                    ->label('Chapters')
                    ->counts('chapters')
                    ->sortable(),
                Tables\Columns\TextColumn::make('average_progress')
                    ->label('Avg Progress')
                    ->suffix('%')
                    ->getStateUsing(function ($record) {
                        return $record->getAverageProgress();
                    })
                    ->sortable(false),
                Tables\Columns\IconColumn::make('is_ctp_course')
                    ->label('CTP Course')
                    ->boolean()
                    ->trueIcon('heroicon-o-star')
                    ->falseIcon('heroicon-o-minus')
                    ->trueColor('warning')
                    ->falseColor('gray')
                    ->getStateUsing(function ($record) {
                        return $record->isCTPCourse();
                    }),
            ])
            ->filters([
                Tables\Filters\Filter::make('ctp_course')
                    ->query(fn ($query) => $query->where('moodle_course_id', config('beaconlms.ctp_course.id')))
                    ->label('CTP Course Only'),
                Tables\Filters\Filter::make('has_enrollments')
                    ->query(fn ($query) => $query->has('enrollments'))
                    ->label('Has Enrollments'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('sync_structure')
                    ->icon('heroicon-o-arrow-path')
                    ->color('info')
                    ->action(function (Course $record) {
                        // This would trigger a sync job to update course structure from Moodle
                        \Filament\Notifications\Notification::make()
                            ->title('Course structure sync initiated')
                            ->info()
                            ->send();
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Course Overview')
                    ->schema([
                        Infolists\Components\ImageEntry::make('thumbnail')
                            ->hiddenLabel(),
                        Infolists\Components\TextEntry::make('title')
                            ->size('lg')
                            ->weight('bold'),
                        Infolists\Components\TextEntry::make('description')
                            ->prose(),
                    ])->columns(1),

                Infolists\Components\Section::make('Course Details')
                    ->schema([
                        Infolists\Components\TextEntry::make('moodle_course_id')
                            ->label('Moodle Course ID'),
                        Infolists\Components\TextEntry::make('duration_days')
                            ->label('Duration')
                            ->suffix(' days'),
                        Infolists\Components\TextEntry::make('total_hours')
                            ->label('Total Hours')
                            ->suffix(' hours'),
                        Infolists\Components\TextEntry::make('total_videos')
                            ->label('Videos'),
                        Infolists\Components\TextEntry::make('total_quizzes')
                            ->label('Quizzes'),
                        Infolists\Components\TextEntry::make('total_xp')
                            ->label('Total XP')
                            ->suffix(' XP')
                            ->getStateUsing(function ($record) {
                                return $record->getTotalXP();
                            }),
                    ])->columns(3),

                Infolists\Components\Section::make('Enrollment Statistics')
                    ->schema([
                        Infolists\Components\TextEntry::make('enrollments_count')
                            ->label('Total Students')
                            ->getStateUsing(function ($record) {
                                return $record->enrollments()->count();
                            }),
                        Infolists\Components\TextEntry::make('average_progress')
                            ->label('Average Progress')
                            ->suffix('%')
                            ->getStateUsing(function ($record) {
                                return $record->getAverageProgress();
                            }),
                        Infolists\Components\TextEntry::make('chapters_count')
                            ->label('Chapters')
                            ->getStateUsing(function ($record) {
                                return $record->chapters()->count();
                            }),
                        Infolists\Components\TextEntry::make('total_items_count')
                            ->label('Total Items')
                            ->getStateUsing(function ($record) {
                                return $record->getTotalItemsCount();
                            }),
                    ])->columns(4),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCourses::route('/'),
            'create' => Pages\CreateCourse::route('/create'),
            'view' => Pages\ViewCourse::route('/{record}'),
            'edit' => Pages\EditCourse::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }
}
