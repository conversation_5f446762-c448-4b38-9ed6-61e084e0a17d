<?php

namespace App\Filament\Resources\CourseResource\Pages;

use App\Filament\Resources\CourseResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;

class ListCourses extends ListRecords
{
    protected static string $resource = CourseResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    public function getTabs(): array
    {
        return [
            'all' => Tab::make('All Courses'),
            'ctp' => Tab::make('CTP Course')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('moodle_course_id', config('beaconlms.ctp_course.id'))),
            'with_enrollments' => Tab::make('Active Courses')
                ->modifyQueryUsing(fn (Builder $query) => $query->has('enrollments')),
        ];
    }
}
