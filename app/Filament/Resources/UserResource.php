<?php

namespace App\Filament\Resources;

use App\Filament\Resources\UserResource\Pages;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Filament\Infolists;
use Filament\Infolists\Infolist;

class UserResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';

    protected static ?string $navigationGroup = 'User Management';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('User Information')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('email')
                            ->email()
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->maxLength(255),
                        Forms\Components\Select::make('role')
                            ->options([
                                'admin' => 'Admin',
                                'student' => 'Student',
                            ])
                            ->required()
                            ->default('student'),
                        Forms\Components\TextInput::make('password')
                            ->password()
                            ->required(fn (string $context): bool => $context === 'create')
                            ->dehydrated(fn ($state) => filled($state))
                            ->minLength(8),
                    ])->columns(2),

                Forms\Components\Section::make('Moodle Integration')
                    ->schema([
                        Forms\Components\TextInput::make('moodle_user_id')
                            ->numeric()
                            ->placeholder('Auto-generated when synced'),
                        Forms\Components\DateTimePicker::make('last_synced_at')
                            ->disabled(),
                    ])->columns(2),

                Forms\Components\Section::make('Engagement & Gamification')
                    ->schema([
                        Forms\Components\TextInput::make('current_streak')
                            ->numeric()
                            ->default(0)
                            ->minValue(0),
                        Forms\Components\DatePicker::make('last_activity_date'),
                        Forms\Components\FileUpload::make('profile_photo')
                            ->image()
                            ->directory('profile-photos')
                            ->maxSize(2048),
                    ])->columns(3),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('profile_photo')
                    ->circular()
                    ->defaultImageUrl(fn (): string => 'https://ui-avatars.com/api/?name=' . urlencode('User') . '&color=7F9CF5&background=EBF4FF'),
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('email')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\BadgeColumn::make('role')
                    ->colors([
                        'success' => 'admin',
                        'primary' => 'student',
                    ]),
                Tables\Columns\TextColumn::make('current_streak')
                    ->label('Streak')
                    ->sortable()
                    ->suffix(' days'),
                Tables\Columns\IconColumn::make('moodle_user_id')
                    ->label('Moodle Sync')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),
                Tables\Columns\TextColumn::make('last_activity_date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('role')
                    ->options([
                        'admin' => 'Admin',
                        'student' => 'Student',
                    ]),
                Tables\Filters\Filter::make('synced_with_moodle')
                    ->query(fn (Builder $query): Builder => $query->whereNotNull('moodle_user_id'))
                    ->label('Synced with Moodle'),
                Tables\Filters\Filter::make('active_users')
                    ->query(fn (Builder $query): Builder => $query->where('last_activity_date', '>=', now()->subDays(7)))
                    ->label('Active (Last 7 days)'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('sync_to_moodle')
                    ->icon('heroicon-o-arrow-path')
                    ->color('warning')
                    ->action(function (User $record) {
                        \App\Jobs\SyncUserToMoodle::dispatch($record);
                        \Filament\Notifications\Notification::make()
                            ->title('Sync job dispatched')
                            ->success()
                            ->send();
                    })
                    ->visible(fn (User $record) => !$record->isSyncedWithMoodle()),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('sync_to_moodle')
                        ->label('Sync to Moodle')
                        ->icon('heroicon-o-arrow-path')
                        ->color('warning')
                        ->action(function ($records) {
                            foreach ($records as $record) {
                                \App\Jobs\SyncUserToMoodle::dispatch($record);
                            }
                            \Filament\Notifications\Notification::make()
                                ->title('Sync jobs dispatched for ' . count($records) . ' users')
                                ->success()
                                ->send();
                        }),
                ]),
            ]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('User Information')
                    ->schema([
                        Infolists\Components\TextEntry::make('name'),
                        Infolists\Components\TextEntry::make('email'),
                        Infolists\Components\TextEntry::make('role')
                            ->badge()
                            ->color(fn (string $state): string => match ($state) {
                                'admin' => 'success',
                                'student' => 'primary',
                            }),
                    ])->columns(3),

                Infolists\Components\Section::make('Moodle Integration')
                    ->schema([
                        Infolists\Components\TextEntry::make('moodle_user_id')
                            ->placeholder('Not synced'),
                        Infolists\Components\TextEntry::make('last_synced_at')
                            ->dateTime()
                            ->placeholder('Never synced'),
                    ])->columns(2),

                Infolists\Components\Section::make('Engagement Statistics')
                    ->schema([
                        Infolists\Components\TextEntry::make('current_streak')
                            ->suffix(' days'),
                        Infolists\Components\TextEntry::make('total_xp')
                            ->label('Total XP')
                            ->suffix(' XP')
                            ->getStateUsing(function ($record) {
                                return $record->getTotalXP();
                            }),
                        Infolists\Components\TextEntry::make('last_activity_date')
                            ->date(),
                        Infolists\Components\TextEntry::make('enrollments_count')
                            ->label('Enrolled Courses')
                            ->getStateUsing(function ($record) {
                                return $record->courseEnrollments()->count();
                            }),
                        Infolists\Components\TextEntry::make('completed_courses_count')
                            ->label('Completed Courses')
                            ->getStateUsing(function ($record) {
                                return $record->courseEnrollments()->where('progress', 100)->count();
                            }),
                        Infolists\Components\TextEntry::make('total_study_hours')
                            ->label('Total Study Hours')
                            ->getStateUsing(function ($record) {
                                return $record->getStudyHours();
                            }),
                    ])->columns(3),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUsers::route('/'),
            'create' => Pages\CreateUser::route('/create'),
            'view' => Pages\ViewUser::route('/{record}'),
            'edit' => Pages\EditUser::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }
}
