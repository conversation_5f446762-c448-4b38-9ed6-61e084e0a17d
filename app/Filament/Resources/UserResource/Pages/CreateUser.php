<?php

namespace App\Filament\Resources\UserResource\Pages;

use App\Filament\Resources\UserResource;
use Filament\Resources\Pages\CreateRecord;

class CreateUser extends CreateRecord
{
    protected static string $resource = UserResource::class;

    protected function afterCreate(): void
    {
        // Automatically sync new students to <PERSON>od<PERSON>
        if ($this->record->role === 'student') {
            \App\Jobs\SyncUserToMoodle::dispatch($this->record);
        }
    }
}
