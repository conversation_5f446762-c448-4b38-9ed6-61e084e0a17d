<?php

namespace App\Filament\Resources\UserResource\Pages;

use App\Filament\Resources\UserResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;

class ListUsers extends ListRecords
{
    protected static string $resource = UserResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    public function getTabs(): array
    {
        return [
            'all' => Tab::make('All Users'),
            'students' => Tab::make('Students')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('role', 'student')),
            'admins' => Tab::make('Admins')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('role', 'admin')),
            'synced' => Tab::make('Synced with Moodle')
                ->modifyQueryUsing(fn (Builder $query) => $query->whereNotNull('moodle_user_id')),
            'unsynced' => Tab::make('Not Synced')
                ->modifyQueryUsing(fn (Builder $query) => $query->whereNull('moodle_user_id')),
        ];
    }
}
