<?php

namespace App\Filament\Widgets;

use App\Models\CourseEnrollment;
use Filament\Widgets\ChartWidget;
use Carbon\Carbon;

class EnrollmentChart extends ChartWidget
{
    protected static ?string $heading = 'Enrollments Over Time';

    protected static ?int $sort = 2;

    protected function getData(): array
    {
        $enrollments = CourseEnrollment::selectRaw('DATE(enrolled_at) as date, COUNT(*) as count')
            ->where('enrolled_at', '>=', now()->subDays(30))
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        $labels = [];
        $data = [];

        // Generate last 30 days
        for ($i = 29; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i)->format('Y-m-d');
            $labels[] = Carbon::now()->subDays($i)->format('M j');
            
            $enrollment = $enrollments->firstWhere('date', $date);
            $data[] = $enrollment ? $enrollment->count : 0;
        }

        return [
            'datasets' => [
                [
                    'label' => 'New Enrollments',
                    'data' => $data,
                    'borderColor' => '#3B82F6',
                    'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                    'fill' => true,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }
}
