<?php

namespace App\Filament\Widgets;

use App\Models\CourseEnrollment;
use Filament\Widgets\ChartWidget;

class Progress<PERSON>hart extends ChartWidget
{
    protected static ?string $heading = 'Progress Distribution';

    protected static ?int $sort = 3;

    protected function getData(): array
    {
        $enrollments = CourseEnrollment::all();

        $progressRanges = [
            '0-20%' => 0,
            '21-40%' => 0,
            '41-60%' => 0,
            '61-80%' => 0,
            '81-100%' => 0,
        ];

        foreach ($enrollments as $enrollment) {
            $progress = $enrollment->progress;
            
            if ($progress <= 20) {
                $progressRanges['0-20%']++;
            } elseif ($progress <= 40) {
                $progressRanges['21-40%']++;
            } elseif ($progress <= 60) {
                $progressRanges['41-60%']++;
            } elseif ($progress <= 80) {
                $progressRanges['61-80%']++;
            } else {
                $progressRanges['81-100%']++;
            }
        }

        return [
            'datasets' => [
                [
                    'label' => 'Students',
                    'data' => array_values($progressRanges),
                    'backgroundColor' => [
                        '#EF4444', // Red for 0-20%
                        '#F97316', // Orange for 21-40%
                        '#EAB308', // Yellow for 41-60%
                        '#22C55E', // Green for 61-80%
                        '#10B981', // Emerald for 81-100%
                    ],
                ],
            ],
            'labels' => array_keys($progressRanges),
        ];
    }

    protected function getType(): string
    {
        return 'doughnut';
    }
}
