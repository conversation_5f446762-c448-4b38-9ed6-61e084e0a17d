<?php

namespace App\Filament\Widgets;

use App\Models\User;
use App\Models\Course;
use App\Models\CourseEnrollment;
use App\Models\UserEngagementLog;
use Filament\Widgets\StatsOverviewWidget as BaseStatsOverviewWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class StatsOverview extends BaseStatsOverviewWidget
{
    protected function getStats(): array
    {
        $totalStudents = User::where('role', 'student')->count();
        $totalCourses = Course::count();
        $totalEnrollments = CourseEnrollment::count();
        $activeEnrollments = CourseEnrollment::whereNull('completed_at')->count();
        $completedEnrollments = CourseEnrollment::whereNotNull('completed_at')->count();
        
        // Calculate completion rate
        $completionRate = $totalEnrollments > 0 ? round(($completedEnrollments / $totalEnrollments) * 100, 1) : 0;
        
        // Calculate average progress
        $averageProgress = CourseEnrollment::avg('progress') ?? 0;
        
        // Get students active in last 7 days
        $activeStudents = User::where('role', 'student')
            ->where('last_activity_date', '>=', now()->subDays(7))
            ->count();

        return [
            Stat::make('Total Students', $totalStudents)
                ->description('Registered students')
                ->descriptionIcon('heroicon-m-users')
                ->color('primary'),

            Stat::make('Active Students', $activeStudents)
                ->description('Active in last 7 days')
                ->descriptionIcon('heroicon-m-user-group')
                ->color('success'),

            Stat::make('Total Courses', $totalCourses)
                ->description('Available courses')
                ->descriptionIcon('heroicon-m-academic-cap')
                ->color('info'),

            Stat::make('Active Enrollments', $activeEnrollments)
                ->description('Students currently enrolled')
                ->descriptionIcon('heroicon-m-clipboard-document-list')
                ->color('warning'),

            Stat::make('Completion Rate', $completionRate . '%')
                ->description('Overall completion rate')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color($completionRate >= 70 ? 'success' : ($completionRate >= 50 ? 'warning' : 'danger')),

            Stat::make('Average Progress', round($averageProgress, 1) . '%')
                ->description('Across all enrollments')
                ->descriptionIcon('heroicon-m-chart-bar')
                ->color($averageProgress >= 70 ? 'success' : ($averageProgress >= 50 ? 'warning' : 'danger')),
        ];
    }
}
