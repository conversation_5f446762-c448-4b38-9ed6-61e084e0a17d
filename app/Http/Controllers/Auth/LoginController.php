<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class LoginController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */

    use AuthenticatesUsers;

    /**
     * Where to redirect users after login.
     *
     * @var string
     */
    protected $redirectTo = '/home';

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest')->except('logout');
        $this->middleware('auth')->only('logout');
    }

    /**
     * Get the post-login redirect path based on user role.
     * Expert implementation with role-based routing and security logging.
     *
     * @return string
     */
    public function redirectTo()
    {
        $user = Auth::user();
        
        if (!$user) {
            return '/login';
        }

        // Log successful login for security audit
        \Log::info('User login successful', [
            'user_id' => $user->id,
            'email' => $user->email,
            'role' => $user->role,
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'timestamp' => now()
        ]);

        // Role-based redirect with fallback security
        return match($user->role) {
            'student' => route('student.dashboard'),
            'admin' => '/admin', // Filament admin panel
            default => '/home' // Fallback for undefined roles
        };
    }

    /**
     * Handle post-authentication logic.
     * Triggers SSO preparation for students.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  mixed  $user
     * @return mixed
     */
    protected function authenticated(Request $request, $user)
    {
        // The MoodleSsoListener will automatically handle SSO URL generation
        // via the Login event that was registered in AppServiceProvider
        
        return redirect()->intended($this->redirectTo());
    }
}
