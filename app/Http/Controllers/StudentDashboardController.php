<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;
use App\Models\Course;
use App\Models\CourseEnrollment;
use App\Models\User;
use App\Models\UserEngagementLog;
use App\Services\MoodleSsoService;
use App\Services\MoodleService;
use App\Services\DashboardStatsService;
use App\Services\UserProgressCalculator;
use App\Services\ExamDateManager;
use App\Services\EngagementTracker;
use App\Repositories\MoodleRepository;
use App\Http\Requests\UpdateExamDateRequest;
use Carbon\Carbon;

/**
 * Student Dashboard Controller
 * 
 * Handles all student dashboard-related operations following clean code principles.
 * Business logic is delegated to dedicated service classes.
 */
class StudentDashboardController extends Controller
{
    public function __construct(
        private readonly MoodleService $moodleService,
        private readonly MoodleRepository $moodleRepository,
        private readonly DashboardStatsService $dashboardStatsService,
        private readonly UserProgressCalculator $progressCalculator,
        private readonly ExamDateManager $examDateManager,
        private readonly EngagementTracker $engagementTracker
    ) {}

    /**
     * Display the student dashboard with statistics and course information
     */
    public function dashboard(): View
    {
        $user = Auth::user();
        
        // Log dashboard visit for engagement tracking
        $this->engagementTracker->logEngagement($user, 'dashboard_visit');
        
        // Get Moodle courses if user is synced
        $moodleCourses = $this->getMoodleCourses($user);
        
        // Get dashboard statistics
        $stats = $this->dashboardStatsService->getDashboardStats($user, $moodleCourses);
        
        // Get additional dashboard data
        $dashboardData = $this->getDashboardData($user, $moodleCourses);
        
        return view('student.dashboard', array_merge($dashboardData, compact('stats')));
    }

    /**
     * Get Moodle courses for user if available
     */
    private function getMoodleCourses(User $user): ?array
    {
        if (!$user->moodle_user_id) {
            return null;
        }
        
        try {
            return $this->moodleService->getUserCourses($user->moodle_user_id);
        } catch (\Exception $e) {
            \Log::error('Failed to fetch Moodle courses', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Get additional dashboard data (enrollments, activities, etc.)
     */
    private function getDashboardData(User $user, ?array $moodleCourses): array
    {
        $enrollments = $this->getEnrollments($user, $moodleCourses);
        $recentActivity = $this->getRecentActivity($user);
        $upcomingExams = $this->getUpcomingExams($user);
        $moodleSsoUrl = $this->getMoodleSsoUrl($user);
        
        return compact('enrollments', 'recentActivity', 'upcomingExams', 'moodleSsoUrl');
    }

    /**
     * Display user's courses
     */
    public function courses(): View
    {
        $user = Auth::user();
        $moodleCourses = $this->getMoodleCourses($user);
        $enrollments = $this->getEnrollments($user, $moodleCourses);
        
        return view('student.courses.index', compact('enrollments'));
    }

    /**
     * Get enrollments from Moodle or local data
     */
    private function getEnrollments(User $user, ?array $moodleCourses): \Illuminate\Support\Collection
    {
        if ($moodleCourses) {
            return collect($moodleCourses)->map(function ($course) {
                return (object) [
                    'id' => $course['id'],
                    'course' => (object) [
                        'id' => $course['id'],
                        'title' => $course['fullname'],
                        'description' => $course['summary'] ?? '',
                        'moodle_course_id' => $course['id'],
                    ],
                    'progress' => 0, // Will be calculated from Moodle data
                    'enrolled_at' => now(),
                ];
            });
        }
        
        return $user->courseEnrollments()->with('course.chapters')->get();
    }

    /**
     * Get recent activity for user
     */
    private function getRecentActivity(User $user): \Illuminate\Support\Collection
    {
        return $user->engagementLogs()
            ->with('course')
            ->latest()
            ->take(5)
            ->get();
    }

    /**
     * Get upcoming exams for user
     */
    private function getUpcomingExams(User $user): \Illuminate\Support\Collection
    {
        return $user->courseEnrollments()
            ->whereNotNull('exam_date')
            ->where('exam_date', '>', now())
            ->with('course')
            ->orderBy('exam_date')
            ->take(3)
            ->get();
    }

    /**
     * Get Moodle SSO URL if available
     */
    private function getMoodleSsoUrl(User $user): ?string
    {
        if ($user->role === 'student') {
            return session('moodle_sso_url');
        }
        
        return null;
    }

    public function chapter($courseId, $chapterId)
    {
        $user = Auth::user();

        // Try to get from Moodle first if user has Moodle ID
        if ($user->moodle_user_id) {
            try {
                $courseContents = $this->moodleService->getCourseContents($courseId);
                $chapter = null;

                foreach ($courseContents as $section) {
                    if ($section['id'] == $chapterId) {
                        $chapter = (object) [
                            'id' => $section['id'],
                            'title' => $section['name'],
                            'summary' => $section['summary'] ?? '',
                            'items' => collect($section['modules'] ?? [])->map(function ($module) {
                                return (object) [
                                    'id' => $module['id'],
                                    'title' => $module['name'],
                                    'type' => $this->mapMoodleModuleType($module['modname']),
                                    'url' => $module['url'] ?? '',
                                    'is_completed' => false, // Would need completion API
                                ];
                            })
                        ];
                        break;
                    }
                }

                if ($chapter) {
                    $enrollment = (object) ['course' => (object) ['id' => $courseId]];
                    return view('student.chapters.show', compact('enrollment', 'chapter'));
                }
            } catch (\Exception $e) {
                \Log::error('Failed to fetch Moodle chapter', ['error' => $e->getMessage()]);
            }
        }

        // Fallback to local data
        $enrollment = $user->courseEnrollments()->where('course_id', $courseId)->firstOrFail();
        $chapter = $enrollment->course->chapters()->with('items')->findOrFail($chapterId);
        return view('student.chapters.show', compact('enrollment', 'chapter'));
    }

    /**
     * Map Moodle module type to our internal types
     */
    private function mapMoodleModuleType(string $modname): string
    {
        $mapping = [
            'quiz' => 'quiz',
            'assign' => 'assignment',
            'resource' => 'resource',
            'url' => 'resource',
            'page' => 'resource',
            'book' => 'resource',
            'scorm' => 'video',
            'h5pactivity' => 'video',
        ];

        return $mapping[$modname] ?? 'resource';
    }

    public function completeItem(Request $request, $courseId, $chapterId, $itemId)
    {
        $user = Auth::user();
        $enrollment = $user->courseEnrollments()->where('course_id', $courseId)->firstOrFail();
        $chapter = $enrollment->course->chapters()->findOrFail($chapterId);
        $item = $chapter->items()->findOrFail($itemId);

        // Mark item as completed
        $item->update(['is_completed' => true]);

        // Log study time
        $studyTime = $request->input('study_time', 1); // Default 1 hour
        UserEngagementLog::updateOrCreate(
            [
                'user_id' => $user->id,
                'course_id' => $courseId,
                'date' => Carbon::today(),
            ],
            [
                'study_hours' => \DB::raw("study_hours + {$studyTime}"),
            ]
        );

        // Update user activity
        $user->updateStreak();

        // Calculate and update course progress
        $totalItems = $enrollment->course->chapters()->withCount('items')->get()->sum('items_count');
        $completedItems = $enrollment->course->chapters()
            ->with('items')
            ->get()
            ->flatMap->items
            ->where('is_completed', true)
            ->count();

        $progress = $totalItems > 0 ? round(($completedItems / $totalItems) * 100, 2) : 0;
        $enrollment->update(['progress' => $progress]);

        return response()->json([
            'success' => true,
            'xp_earned' => $item->xp_points,
            'progress' => $progress,
        ]);
    }

    public function profile()
    {
        $user = Auth::user();

        // Get user stats from real data
        $enrollments = $user->courseEnrollments()->with('course.chapters.items')->get();
        $totalActivitiesCompleted = $enrollments->sum(function ($enrollment) {
            return $enrollment->course->chapters->sum(function ($chapter) {
                return $chapter->items->where('is_completed', true)->count();
            });
        });

        $stats = [
            'total_xp' => $user->getTotalXP() ?? 0,
            'current_streak' => $user->current_streak ?? 0,
            'total_study_hours' => $user->getStudyHours() ?? 0,
            'courses_completed' => $enrollments->where('progress', 100)->count(),
            'courses_enrolled' => $enrollments->count(),
            'average_progress' => round($enrollments->avg('progress') ?? 0, 1),
            'total_activities_completed' => $totalActivitiesCompleted,
            'last_synced' => $user->last_synced_at ? $user->last_synced_at->diffForHumans() : 'Never synced',
        ];

        // Get achievements based on real progress
        $achievements = $this->getAchievements($user, $enrollments, $totalActivitiesCompleted);

        // Get study streak data for the last 30 days
        $streakData = UserEngagementLog::where('user_id', $user->id)
            ->where('date', '>=', Carbon::now()->subDays(30))
            ->orderBy('date')
            ->get()
            ->pluck('study_hours', 'date');

        return view('student.profile', compact('stats', 'achievements', 'streakData'));
    }

    /**
     * Get user achievements based on their actual progress
     */
    private function getAchievements($user, $enrollments, $totalActivitiesCompleted)
    {
        $achievements = [];

        // First Steps - Complete first lesson
        if ($totalActivitiesCompleted > 0) {
            $achievements[] = [
                'name' => 'First Steps',
                'description' => 'Complete your first lesson',
                'earned_at' => $user->created_at,
                'icon' => '🎯'
            ];
        }

        // Streak Master - Maintain a 7-day streak
        if ($user->current_streak >= 7) {
            $achievements[] = [
                'name' => 'Streak Master',
                'description' => 'Maintain a 7-day study streak',
                'earned_at' => now()->subDays(7),
                'icon' => '🔥'
            ];
        }

        // Course Completer - Complete a full course
        if ($enrollments->where('progress', 100)->count() > 0) {
            $achievements[] = [
                'name' => 'Course Completer',
                'description' => 'Complete your first full course',
                'earned_at' => $enrollments->where('progress', 100)->first()->updated_at ?? now(),
                'icon' => '🏆'
            ];
        }

        // XP Collector - Earn 1000 XP
        if ($user->getTotalXP() >= 1000) {
            $achievements[] = [
                'name' => 'XP Collector',
                'description' => 'Earn 1000 XP points',
                'earned_at' => now(),
                'icon' => '💎'
            ];
        }

        // Progress Master - Achieve 80% average progress
        if ($enrollments->avg('progress') >= 80) {
            $achievements[] = [
                'name' => 'Progress Master',
                'description' => 'Achieve 80% average progress across all courses',
                'earned_at' => now(),
                'icon' => '⭐'
            ];
        }

        return $achievements;
    }

    /**
     * Manually trigger progress sync from Moodle
     */
    public function syncProgress()
    {
        $user = Auth::user();

        // Check if user has Moodle ID
        if (!$user->moodle_user_id) {
            return response()->json([
                'success' => false,
                'message' => 'No Moodle account linked. Please contact support.'
            ], 400);
        }

        // Check rate limiting (max once every 5 minutes)
        if ($user->last_synced_at && $user->last_synced_at->diffInMinutes(now()) < 5) {
            return response()->json([
                'success' => false,
                'message' => 'Please wait before syncing again. Last sync was ' . $user->last_synced_at->diffForHumans() . '.'
            ], 429);
        }

        try {
            // Dispatch sync job
            \App\Jobs\SyncStudentProgress::dispatch($user);

            return response()->json([
                'success' => true,
                'message' => 'Progress sync started. This may take a few moments to complete.'
            ]);

        } catch (\Exception $e) {
            \Log::error('Manual sync failed', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Sync failed. Please try again later.'
            ], 500);
        }
    }

    /**
     * Launch Moodle with SSO authentication
     */
    public function launchMoodle(MoodleSsoService $ssoService)
    {
        $user = Auth::user();

        try {
            // Log engagement for Moodle launch
            $this->logEngagement($user, 'moodle_launch');

            // Get or generate SSO URL
            $ssoUrl = $ssoService->getCachedSsoUrl($user) ?? $ssoService->generateSsoUrl($user);

            if (!$ssoUrl) {
                return redirect()->route('student.dashboard')
                    ->with('error', 'Unable to connect to Moodle. Please try again later.');
            }

            // Log the SSO launch for analytics
            \Log::info('Student launched Moodle via SSO', [
                'user_id' => $user->id,
                'email' => $user->email,
                'timestamp' => now()
            ]);

            // Redirect to Moodle with SSO authentication
            return redirect()->away($ssoUrl);

        } catch (\Exception $e) {
            \Log::error('Moodle SSO launch failed', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);

            return redirect()->route('student.dashboard')
                ->with('error', 'Unable to connect to Moodle. Please contact support if this persists.');
        }
    }

    /**
     * Launch specific Moodle course
     */
    public function launchMoodleCourse(MoodleSsoService $ssoService, $courseId = null)
    {
        $user = Auth::user();

        try {
            // Use CTP course ID if not specified
            $courseId = $courseId ?? config('beaconlms.ctp_course.id');

            // Log engagement for course launch
            $this->logEngagement($user, 'course_launch', $courseId);

            // Get SSO URL with course redirect
            $ssoUrl = $ssoService->generateSsoUrl($user, $courseId);

            if (!$ssoUrl) {
                return redirect()->route('student.dashboard')
                    ->with('error', 'Unable to connect to Moodle course. Please try again later.');
            }

            // Log the course launch
            \Log::info('Student launched Moodle course via SSO', [
                'user_id' => $user->id,
                'course_id' => $courseId,
                'timestamp' => now()
            ]);

            return redirect()->away($ssoUrl);

        } catch (\Exception $e) {
            \Log::error('Moodle course launch failed', [
                'user_id' => $user->id,
                'course_id' => $courseId,
                'error' => $e->getMessage()
            ]);

            return redirect()->route('student.dashboard')
                ->with('error', 'Unable to connect to Moodle course. Please contact support.');
        }
    }

    /**
     * Log user engagement activity
     */
    private function logEngagement($user, $activity, $courseId = null)
    {
        try {
            // Get course for engagement logging
            $course = null;
            if ($courseId) {
                $course = Course::where('moodle_course_id', $courseId)->first();
            } else {
                // Default to CTP course
                $ctpCourseId = config('beaconlms.ctp_course.id');
                $course = Course::where('moodle_course_id', $ctpCourseId)->first();
            }

            if ($course) {
                // Log study time based on activity type
                $studyTime = $this->getStudyTimeForActivity($activity);

                if ($studyTime > 0) {
                    UserEngagementLog::logStudyTime($user, $course, $studyTime);
                }
            }

            // Log activity for analytics
            \Log::info('User engagement logged', [
                'user_id' => $user->id,
                'activity' => $activity,
                'course_id' => $courseId,
                'timestamp' => now()
            ]);

        } catch (\Exception $e) {
            \Log::warning('Failed to log engagement', [
                'user_id' => $user->id,
                'activity' => $activity,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get study time for different activities
     */
    private function getStudyTimeForActivity($activity): float
    {
        $activityTimes = [
            'dashboard_visit' => 0.05, // 3 minutes
            'moodle_launch' => 0.1,    // 6 minutes
            'course_launch' => 0.25,   // 15 minutes
            'chapter_view' => 0.5,     // 30 minutes
            'quiz_attempt' => 1.0,     // 1 hour
            'assignment_submit' => 2.0, // 2 hours
        ];

        return $activityTimes[$activity] ?? 0;
    }

    /**
     * Generate Moodle course URL
     */
    private function generateMoodleCourseUrl($courseId): string
    {
        $moodleUrl = config('beaconlms.moodle.url');
        return rtrim($moodleUrl, '/') . '/course/view.php?id=' . $courseId;
    }

    /**
     * Get activities completed today
     */
    private function getActivitiesCompletedToday($user): int
    {
        return $user->courseEnrollments()
            ->with('course.chapters.items')
            ->get()
            ->flatMap(function ($enrollment) {
                return $enrollment->course->chapters->flatMap->items;
            })
            ->where('is_completed', true)
            ->where('updated_at', '>=', now()->startOfDay())
            ->count();
    }

    /**
     * Get weekly progress trend
     */
    private function getWeeklyProgressTrend($user): array
    {
        $startDate = now()->startOfWeek();
        $endDate = now()->endOfWeek();

        return UserEngagementLog::getStudyHoursForPeriod($user, $startDate, $endDate);
    }
    public function showExamDateForm()
    {
        $user = Auth::user();

        // Get user's CTP course enrollment
        $enrollment = $user->courseEnrollments()
            ->whereHas('course', function ($query) {
                $query->where('moodle_course_id', config('beaconlms.ctp_course.id'));
            })
            ->first();

        // If no enrollment exists, create one
        if (!$enrollment) {
            $ctpCourse = Course::where('moodle_course_id', config('beaconlms.ctp_course.id'))->first();
            if (!$ctpCourse) {
                // Create the CTP course if it doesn't exist
                $ctpCourse = Course::create([
                    'title' => config('beaconlms.ctp_course.title'),
                    'description' => config('beaconlms.ctp_course.description'),
                    'moodle_course_id' => config('beaconlms.ctp_course.id'),
                    'is_active' => true,
                ]);
            }

            // Create enrollment
            $enrollment = CourseEnrollment::create([
                'user_id' => $user->id,
                'course_id' => $ctpCourse->id,
                'progress' => 0,
                'enrolled_at' => now(),
                'exam_date' => now()->addDays(config('beaconlms.ctp_course.exam_preparation_days', 43)),
            ]);
        }

        // Get current exam date (use user's date or default from config)
        $currentExamDate = $enrollment->exam_date ?? now()->addDays(config('beaconlms.ctp_course.exam_preparation_days', 43));
        $defaultDays = config('beaconlms.ctp_course.exam_preparation_days', 43);

        return view('student.exam-date-form', compact('enrollment', 'currentExamDate', 'defaultDays'));
    }

    /**
     * Update the user's exam date
     */
    public function updateExamDate(Request $request)
    {
        $request->validate([
            'exam_date' => 'required|date|after:today',
        ], [
            'exam_date.required' => 'Please select an exam date.',
            'exam_date.date' => 'Please enter a valid date.',
            'exam_date.after' => 'Exam date must be in the future.',
        ]);

        $user = Auth::user();
        $newExamDate = Carbon::parse($request->exam_date);

        try {
            // Use ExamDateManager to update exam date (this will fire events)
            $enrollment = $this->examDateManager->updateExamDate($user, $newExamDate);

            // Log engagement for exam date update
            $this->logEngagement($user, 'exam_date_update');

            return redirect()->route('student.dashboard')
                ->with('success', 'Your exam date has been updated successfully!');

        } catch (\Exception $e) {
            \Log::error('Failed to update exam date', [
                'user_id' => $user->id,
                'exam_date' => $newExamDate,
                'error' => $e->getMessage()
            ]);

            return redirect()->back()
                ->with('error', 'Failed to update exam date. Please try again.')
                ->withInput();
        }
    }
}
