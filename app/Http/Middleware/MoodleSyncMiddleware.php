<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class MoodleSyncMiddleware
{
    /**
     * Handle an incoming request.
     * Ensures user is synced with <PERSON><PERSON><PERSON> before accessing certain features.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = auth()->user();

        // Check if user is synced with <PERSON><PERSON><PERSON>
        if (!$user || !$user->moodle_user_id) {
            return redirect()->route('student.sync-required')
                ->with('warning', 'Your account needs to be synced with <PERSON><PERSON><PERSON> to access this feature.');
        }

        // Check if sync is recent (within last 24 hours)
        if ($user->last_synced_at && $user->last_synced_at->diffInHours(now()) > 24) {
            // Trigger background sync job if sync is stale
            dispatch(new \App\Jobs\SyncStudentProgress($user));
        }

        return $next($request);
    }
}
