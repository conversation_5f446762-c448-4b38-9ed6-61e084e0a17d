<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Carbon\Carbon;

/**
 * Request validation for exam date updates
 */
class UpdateExamDateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $minimumDays = config('beaconlms.business_rules.minimum_preparation_days', 30);
        $minimumDate = now()->addDays($minimumDays)->format('Y-m-d');

        return [
            'exam_date' => [
                'required',
                'date',
                'after:today',
                "after_or_equal:{$minimumDate}",
            ],
        ];
    }

    /**
     * Get custom error messages for validation rules.
     */
    public function messages(): array
    {
        $minimumDays = config('beaconlms.business_rules.minimum_preparation_days', 30);

        return [
            'exam_date.required' => 'Please select an exam date.',
            'exam_date.date' => 'Please enter a valid date.',
            'exam_date.after' => 'Exam date must be in the future.',
            'exam_date.after_or_equal' => "Exam date must be at least {$minimumDays} days from now for proper preparation.",
        ];
    }

    /**
     * Get the validated exam date as Carbon instance
     */
    public function getExamDate(): Carbon
    {
        return Carbon::parse($this->validated()['exam_date']);
    }
}
