<?php

namespace App\Jobs;

use App\Models\User;
use App\Services\MoodleService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class SyncStudentProgress implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected User $user;

    /**
     * Create a new job instance.
     */
    public function __construct(User $user)
    {
        $this->user = $user;
    }

    /**
     * Execute the job.
     */
    public function handle(MoodleService $moodleService): void
    {
        $startTime = microtime(true);
        $coursesProcessed = 0;
        $activitiesProcessed = 0;

        try {
            Log::info('Starting progress sync for user', [
                'user_id' => $this->user->id,
                'email' => $this->user->email,
                'moodle_user_id' => $this->user->moodle_user_id
            ]);

            // Skip if user doesn't have Moodle ID
            if (!$this->user->moodle_user_id) {
                Log::warning('User has no Moodle ID, skipping sync', ['user_id' => $this->user->id]);
                return;
            }

            // Test Moodle connection first
            try {
                $moodleService->testConnection();
            } catch (\Exception $e) {
                Log::error('Moodle connection test failed', [
                    'user_id' => $this->user->id,
                    'error' => $e->getMessage()
                ]);
                throw new \Exception('Cannot connect to Moodle: ' . $e->getMessage());
            }

            // Get user's enrolled courses from Moodle with retry logic
            $moodleCourses = $this->retryOperation(function () use ($moodleService) {
                return $moodleService->getUserCourses($this->user->moodle_user_id);
            }, 3);

            if (empty($moodleCourses)) {
                Log::info('No courses found for user in Moodle', ['user_id' => $this->user->id]);
                $this->user->update(['last_synced_at' => Carbon::now()]);
                return;
            }

            Log::info('Found courses to sync', [
                'user_id' => $this->user->id,
                'course_count' => count($moodleCourses)
            ]);

            foreach ($moodleCourses as $moodleCourse) {
                try {
                    $activities = $this->syncCourseProgress($moodleService, $moodleCourse);
                    $coursesProcessed++;
                    $activitiesProcessed += $activities;
                } catch (\Exception $e) {
                    Log::warning('Failed to sync course', [
                        'user_id' => $this->user->id,
                        'course_id' => $moodleCourse['id'] ?? 'unknown',
                        'error' => $e->getMessage()
                    ]);
                    // Continue with other courses
                }
            }

            // Update last synced timestamp
            $this->user->update(['last_synced_at' => Carbon::now()]);

            $duration = round((microtime(true) - $startTime) * 1000, 2);

            Log::info('Progress sync completed for user', [
                'user_id' => $this->user->id,
                'courses_processed' => $coursesProcessed,
                'activities_processed' => $activitiesProcessed,
                'duration_ms' => $duration
            ]);

        } catch (\Exception $e) {
            $duration = round((microtime(true) - $startTime) * 1000, 2);

            Log::error('Progress sync failed for user', [
                'user_id' => $this->user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'courses_processed' => $coursesProcessed,
                'activities_processed' => $activitiesProcessed,
                'duration_ms' => $duration
            ]);

            // Re-throw to mark job as failed
            throw $e;
        }
    }

    /**
     * Retry an operation with exponential backoff
     */
    private function retryOperation(callable $operation, int $maxRetries = 3, int $baseDelay = 1000)
    {
        $attempt = 1;
        
        while ($attempt <= $maxRetries) {
            try {
                return $operation();
            } catch (\Exception $e) {
                if ($attempt === $maxRetries) {
                    throw $e;
                }
                
                $delay = $baseDelay * pow(2, $attempt - 1); // Exponential backoff
                Log::warning("Operation failed, retrying in {$delay}ms", [
                    'attempt' => $attempt,
                    'max_retries' => $maxRetries,
                    'error' => $e->getMessage()
                ]);
                
                usleep($delay * 1000); // Convert to microseconds
                $attempt++;
            }
        }
    }

    /**
     * Sync progress for a specific course
     */
    private function syncCourseProgress(MoodleService $moodleService, array $moodleCourse): void
    {
        // Find local course by Moodle course ID
        $localCourse = \App\Models\Course::where('moodle_course_id', $moodleCourse['id'])->first();
        
        if (!$localCourse) {
            Log::warning('Local course not found for Moodle course', [
                'moodle_course_id' => $moodleCourse['id']
            ]);
            return;
        }

        // Find or create enrollment
        $enrollment = \App\Models\CourseEnrollment::firstOrCreate([
            'user_id' => $this->user->id,
            'course_id' => $localCourse->id,
        ], [
            'moodle_enrollment_id' => null,
            'progress' => 0.00,
            'enrolled_at' => Carbon::now(),
            'exam_date' => Carbon::now()->addDays((int)config('beaconlms.ctp_course.exam_preparation_days')),
        ]);

        // Get completion status from Moodle
        $completion = $moodleService->getCourseCompletion(
            $this->user->moodle_user_id,
            $moodleCourse['id']
        );

        // Calculate progress percentage
        $progress = $this->calculateProgress($completion);

        // Update enrollment progress
        $enrollment->update(['progress' => $progress]);

        // Sync activity completion
        $this->syncActivityCompletion($moodleService, $moodleCourse['id'], $localCourse);

        Log::info('Course progress synced', [
            'user_id' => $this->user->id,
            'course_id' => $localCourse->id,
            'progress' => $progress
        ]);
    }

    /**
     * Calculate progress percentage from Moodle completion data
     */
    private function calculateProgress(array $completion): float
    {
        if (empty($completion['completions'])) {
            return 0.00;
        }

        $totalActivities = count($completion['completions']);
        $completedActivities = 0;

        foreach ($completion['completions'] as $activityCompletion) {
            if ($activityCompletion['state'] == 1) { // Completed
                $completedActivities++;
            }
        }

        return $totalActivities > 0 ? round(($completedActivities / $totalActivities) * 100, 2) : 0.00;
    }

    /**
     * Sync individual activity completion status
     */
    private function syncActivityCompletion(MoodleService $moodleService, int $moodleCourseId, $localCourse): void
    {
        try {
            $activities = $moodleService->getActivitiesCompletion(
                $this->user->moodle_user_id,
                $moodleCourseId
            );

            foreach ($activities['statuses'] ?? [] as $activity) {
                // Find local chapter item by Moodle activity ID
                $chapterItem = \App\Models\ChapterItem::where('moodle_activity_id', $activity['cmid'])->first();
                
                if ($chapterItem) {
                    $isCompleted = $activity['state'] == 1;
                    $chapterItem->update(['is_completed' => $isCompleted]);
                }
            }

        } catch (\Exception $e) {
            Log::warning('Failed to sync activity completion', [
                'user_id' => $this->user->id,
                'course_id' => $moodleCourseId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Handle job failure
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('SyncStudentProgress job failed', [
            'user_id' => $this->user->id,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}
