<?php

namespace App\Jobs;

use App\Models\User;
use App\Models\Course;
use App\Models\CourseEnrollment;
use App\Models\CourseChapter;
use App\Models\ChapterItem;
use App\Services\MoodleService;
use App\Repositories\MoodleRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Exception;

class SyncUserToMoodle implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected User $user;
    protected bool $autoEnroll;

    /**
     * Create a new job instance.
     */
    public function __construct(User $user, bool $autoEnroll = true)
    {
        $this->user = $user;
        $this->autoEnroll = $autoEnroll;
    }

    /**
     * Execute the job.
     */
    public function handle(MoodleService $moodleService): void
    {
        try {
            Log::info('Starting user sync to Moodle', ['user_id' => $this->user->id, 'auto_enroll' => $this->autoEnroll]);

            // Skip if user already has Moodle ID
            if ($this->user->moodle_user_id) {
                Log::info('User already has Moodle ID, checking enrollment', [
                    'user_id' => $this->user->id,
                    'moodle_user_id' => $this->user->moodle_user_id
                ]);
                $this->syncCourseToDatabase( $moodleService, config('beaconlms.ctp_course.id'));
                return;
            }

            // Check if user already exists in Moodle by email
            Log::info('Checking if user exists in Moodle', ['user_id' => $this->user->id, 'email' => $this->user->email]);
            $existingUsers = $moodleService->getUsersByField('email', [$this->user->email]);

            if (!empty($existingUsers)) {
                // User exists, update local record with Moodle ID
                $moodleUser = $existingUsers[0];
                $this->user->update(['moodle_user_id' => $moodleUser['id']]);

                Log::info('Found existing Moodle user', [
                    'user_id' => $this->user->id,
                    'moodle_user_id' => $moodleUser['id']
                ]);
            } else {
                // Create new user in Moodle
                Log::info('Creating new Moodle user', ['user_id' => $this->user->id]);
                $moodleUser = $this->createMoodleUser($moodleService);
                $this->user->update(['moodle_user_id' => $moodleUser['id']]);

                Log::info('Created new Moodle user', [
                    'user_id' => $this->user->id,
                    'moodle_user_id' => $moodleUser['id']
                ]);
            }

            // Auto-enroll in CTP course if user is a student
            if ($this->user->role === 'student') {
                Log::info('Enrolling student in CTP course', ['user_id' => $this->user->id]);
                $this->enrollInCTPCourse($moodleService);
            }

            Log::info('User sync to Moodle completed', ['user_id' => $this->user->id]);

        } catch (\Exception $e) {
            Log::error('User sync to Moodle failed', [
                'user_id' => $this->user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    /**
     * Create a new user in Moodle
     */
    private function createMoodleUser(MoodleService $moodleService): array
    {
        $nameParts = explode(' ', $this->user->name, 2);
        $firstname = $nameParts[0];
        $lastname = $nameParts[1] ?? '';

        $userData = [
            'username' => $this->generateUsername(),
            'password' => Str::random(12), // Generate random password
            'firstname' => $firstname,
            'lastname' => $lastname,
            'email' => $this->user->email,
        ];

        $result = $moodleService->createUsers([$userData]);

        if (empty($result)) {
            throw new Exception('Failed to create user in Moodle');
        }

        return $result[0];
    }

    /**
     * Generate unique username for Moodle
     */
    private function generateUsername(): string
    {
        $baseUsername = Str::slug(explode('@', $this->user->email)[0]);
        $username = $baseUsername;
        $counter = 1;

        // Ensure username is unique (basic implementation)
        while (strlen($username) < 3) {
            $username = $baseUsername . $counter;
            $counter++;
        }

        return $username;
    }

    /**
     * Auto-enroll user in CTP course with fallback methods
     */
    private function enrollInCTPCourse(MoodleService $moodleService): void
    {
        Log::info('Starting CTP course enrollment process', [
            'user_id' => $this->user->id,
            'moodle_user_id' => $this->user->moodle_user_id
        ]);

        try {
            $ctpCourseId = config('beaconlms.ctp_course.id');
            Log::info('CTP Course configuration', [
                'course_id' => $ctpCourseId,
                'course_id_type' => gettype($ctpCourseId)
            ]);

            if (!$ctpCourseId) {
                Log::warning('CTP course ID not configured', [
                    'user_id' => $this->user->id,
                    'config_value' => config('beaconlms.ctp_course.id')
                ]);
                return;
            }

            if (!$this->user->moodle_user_id) {
                Log::error('Cannot enroll user - no Moodle user ID', [
                    'user_id' => $this->user->id
                ]);
                return;
            }

            // Try manual enrollment first
            if ($this->tryManualEnrollment($moodleService, $ctpCourseId)) {
                Log::info('User successfully enrolled in CTP course via manual enrollment', [
                    'user_id' => $this->user->id,
                    'moodle_user_id' => $this->user->moodle_user_id,
                    'course_id' => $ctpCourseId
                ]);

                return;
            }


            // Sync course data to local database after successful enrollment
            $this->syncCourseToDatabase($moodleService, $ctpCourseId);

            // Create enrollment record in local database
            $this->createLocalEnrollment($ctpCourseId);

            Log::warning('All enrollment methods failed for CTP course', [
                'user_id' => $this->user->id,
                'moodle_user_id' => $this->user->moodle_user_id,
                'course_id' => $ctpCourseId
            ]);

        } catch (\Exception $e) {
            // Check if it's a manual enrollment plugin issue
            if (strpos($e->getMessage(), 'Manual enrolment plugin instance doesn\'t exist or is disabled') !== false) {
                Log::error('Moodle enrollment failed - Manual enrollment not enabled for CTP course', [
                    'user_id' => $this->user->id,
                    'moodle_user_id' => $this->user->moodle_user_id,
                    'course_id' => config('beaconlms.ctp_course.id'),
                    'error' => $e->getMessage(),
                    'solution' => 'Enable manual enrollment for your CTP course in Moodle',
                    'instructions' => 'Go to Moodle Admin > Courses > Your CTP Course > Settings > Enable Manual enrolments',
                    'alternative' => 'Alternative: Enable "Guest access" or "Self enrolment" for the course'
                ]);
            }
            // Check if it's an access control exception
            else if (strpos($e->getMessage(), 'Access control exception') !== false) {
                Log::error('Moodle enrollment failed - Missing web service permissions', [
                    'user_id' => $this->user->id,
                    'moodle_user_id' => $this->user->moodle_user_id,
                    'error' => $e->getMessage(),
                    'solution' => 'Enable enrol_manual_enrol_users function in Moodle web service',
                    'instructions' => 'Go to Moodle Admin > Plugins > Web services > External services > Add enrol_manual_enrol_users function'
                ]);
            } else {
                Log::error('Failed to auto-enroll user in CTP course', [
                    'user_id' => $this->user->id,
                    'moodle_user_id' => $this->user->moodle_user_id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }
        }
    }

    /**
     * Try manual enrollment method
     */
    private function tryManualEnrollment(MoodleService $moodleService, int $courseId): bool
    {
        try {
            Log::info('Attempting manual enrollment', [
                'user_id' => $this->user->id,
                'course_id' => $courseId
            ]);

            $enrollments = [
                [
                    'userid' => $this->user->moodle_user_id,
                    'courseid' => $courseId,
                    'roleid' => 5, // Student role
                ]
            ];

            $result = $moodleService->enrollUsers($enrollments);

            return $result && is_array($result);

        } catch (\Exception $e) {
            Log::info('Manual enrollment failed, trying alternative methods', [
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Sync course data from Moodle to local database
     */
    private function syncCourseToDatabase(MoodleService $moodleService, int $courseId): void
    {
        try {
            Log::info('Starting course sync to database', [
                'course_id' => $courseId,
                'user_id' => $this->user->id
            ]);

            // Check if course already exists in local database
            $localCourse = Course::where('moodle_course_id', $courseId)->first();

            if (!$localCourse) {
                // Get course details from Moodle
                $moodleCourses = $moodleService->getCourses();
                $moodleCourse = collect($moodleCourses)->firstWhere('id', $courseId);

                if ($moodleCourse) {
                    // Create course in local database
                    $localCourse = Course::create([
                        'title' => $moodleCourse['fullname'],
                        'description' => $moodleCourse['summary'] ?? '',
                        'moodle_course_id' => $courseId,
                        'duration_days' => 30, // Default
                        'total_hours' => 120,   // Default
                    ]);

                    Log::info('Created course in local database', [
                        'local_course_id' => $localCourse->id,
                        'moodle_course_id' => $courseId
                    ]);

                }
            } else {
                Log::info('Course already exists in local database', [
                    'local_course_id' => $localCourse->id,
                    'moodle_course_id' => $courseId
                ]);
            }
            // Sync course structure (chapters and items)
            $this->syncCourseStructure($moodleService, $localCourse, $courseId);

        } catch (\Exception $e) {
            Log::error('Failed to sync course to database', [
                'course_id' => $courseId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Sync course structure (chapters and items) from Moodle
     */
    private function syncCourseStructure(MoodleService $moodleService, Course $localCourse, int $courseId): void
    {
        try {
            Log::info('Starting course structure sync', [
                'course_id' => $courseId,
                'local_course_id' => $localCourse->id
            ]);

            // Get course contents from Moodle
            $courseContents = $moodleService->getCourseContents($courseId);

            foreach ($courseContents as $index => $section) {
                // Skip if section has no name or is hidden
                if (empty($section['name']) || (isset($section['visible']) && !$section['visible'])) {
                    continue;
                }
                Log::info($section);
                // Create or update chapter
                $chapter = CourseChapter::updateOrCreate(
                    [
                        'course_id' => $localCourse->id,
                        'moodle_section_id' => $section['id']
                    ],
                    [
                        'title' => $section['name'],
                        'description' => $section['summary'] ?? '',
                        'order' => $index + 1
                    ]
                );

                Log::info('Synced course chapter', [
                    'chapter_id' => $chapter->id,
                    'moodle_section_id' => $section['id'],
                    'title' => $section['name']
                ]);

                // Sync chapter items (activities)
                if (!empty($section['modules'])) {
                    $this->syncChapterItems($chapter, $section['modules']);
                }
            }

        } catch (\Exception $e) {
            Log::error('Failed to sync course structure', [
                'course_id' => $courseId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Sync chapter items (activities) from Moodle
     */
    private function syncChapterItems(CourseChapter $chapter, array $modules): void
    {
        try {
            foreach ($modules as $index => $module) {
                // Skip if module is not visible
                if (isset($module['visible']) && !$module['visible']) {
                    continue;
                }

                // Map Moodle module type to our internal type
                $itemType = $this->mapMoodleModuleType($module['modname']);

                // Calculate XP points based on activity type
                $xpPoints = $this->calculateXPForActivity($module['modname']);

                // Create or update chapter item
                $item = ChapterItem::updateOrCreate(
                    [
                        'course_chapter_id' => $chapter->id,
                        'moodle_activity_id' => $module['id']
                    ],
                    [
                        'title' => $module['name'],
                        'type' => $itemType,
                        'xp_points' => $xpPoints,
                        'order' => $index + 1,
                        'is_completed' => false
                    ]
                );

                Log::info('Synced chapter item', [
                    'item_id' => $item->id,
                    'moodle_activity_id' => $module['id'],
                    'title' => $module['name'],
                    'type' => $itemType
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Failed to sync chapter items', [
                'chapter_id' => $chapter->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Create enrollment record in local database
     */
    private function createLocalEnrollment(int $courseId): void
    {
        try {
            // Find the local course
            $localCourse = Course::where('moodle_course_id', $courseId)->first();

            if (!$localCourse) {
                Log::warning('Local course not found for enrollment', [
                    'moodle_course_id' => $courseId,
                    'user_id' => $this->user->id
                ]);
                return;
            }

            // Check if enrollment already exists
            $existingEnrollment = CourseEnrollment::where('user_id', $this->user->id)
                ->where('course_id', $localCourse->id)
                ->first();

            if ($existingEnrollment) {
                Log::info('Enrollment already exists', [
                    'enrollment_id' => $existingEnrollment->id,
                    'user_id' => $this->user->id,
                    'course_id' => $localCourse->id
                ]);
                return;
            }

            // Create new enrollment
            $enrollment = CourseEnrollment::create([
                'user_id' => $this->user->id,
                'course_id' => $localCourse->id,
                'progress' => 0.00,
                'enrolled_at' => now(),
                'exam_date' => now()->addDays((int)config('beaconlms.ctp_course.exam_preparation_days', 30))
            ]);

            Log::info('Created local enrollment record', [
                'enrollment_id' => $enrollment->id,
                'user_id' => $this->user->id,
                'course_id' => $localCourse->id,
                'exam_date' => $enrollment->exam_date
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to create local enrollment', [
                'user_id' => $this->user->id,
                'moodle_course_id' => $courseId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Map Moodle module type to our internal types
     */
    private function mapMoodleModuleType(string $modname): string
    {
        $mapping = [
            'quiz' => 'quiz',
            'assign' => 'assignment',
            'resource' => 'resource',
            'url' => 'resource',
            'page' => 'resource',
            'book' => 'resource',
            'scorm' => 'video',
            'h5pactivity' => 'video',
        ];

        return $mapping[$modname] ?? 'resource';
    }

    /**
     * Calculate XP points for activity type
     */
    private function calculateXPForActivity(string $modname): int
    {
        $xpMapping = [
            'quiz' => config('beaconlms.xp_points.quiz_passed', 50),
            'assign' => config('beaconlms.xp_points.assignment_submitted', 30),
            'scorm' => config('beaconlms.xp_points.video_completion', 20),
            'h5pactivity' => config('beaconlms.xp_points.video_completion', 20),
            'resource' => 10,
            'url' => 10,
            'page' => 15,
            'book' => 25,
        ];

        return $xpMapping[$modname] ?? 10;
    }

    /**
     * Handle job failure
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('SyncUserToMoodle job failed', [
            'user_id' => $this->user->id,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}
