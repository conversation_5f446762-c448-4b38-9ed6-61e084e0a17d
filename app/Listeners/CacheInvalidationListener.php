<?php

namespace App\Listeners;

use App\Events\DataUpdatedEvent;
use App\Services\CacheManager;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

/**
 * Listener for handling cache invalidation when data is updated
 */
class CacheInvalidationListener implements ShouldQueue
{
    use InteractsWithQueue;

    public function __construct(
        private readonly CacheManager $cacheManager
    ) {}

    /**
     * Handle the event.
     */
    public function handle(DataUpdatedEvent $event): void
    {
        try {
            // Invalidate specific cache types based on event
            $this->cacheManager->invalidateUserCache(
                $event->user,
                $event->affectedCacheTypes
            );

            // Handle specific event types
            $this->cacheManager->handleDataUpdate($event->eventType, [
                'user_id' => $event->user->id,
                ...$event->data
            ]);

            Log::info('Cache invalidated via event', [
                'event_type' => $event->eventType,
                'user_id' => $event->user->id,
                'cache_types' => $event->affectedCacheTypes
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to invalidate cache via event', [
                'event_type' => $event->eventType,
                'user_id' => $event->user->id,
                'error' => $e->getMessage()
            ]);
        }
    }
}
