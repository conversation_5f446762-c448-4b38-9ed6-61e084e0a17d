<?php

namespace App\Listeners;

use App\Jobs\SyncUserToMoodle;
use App\Services\MoodleSsoService;
use Illuminate\Auth\Events\Login;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class MoodleSsoListener
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(Login $event): void
    {
        // We only care about students for SSO
        if ($event->user->role === 'student') {
            $ssoService = new MoodleSsoService();
            $ssoUrl = $ssoService->generateSsoUrl($event->user);

            // Dispatch job to sync user to <PERSON><PERSON><PERSON> and enroll in CTP course
            \App\Jobs\SyncUserToMoodle::dispatch($event->user, true);

            if ($ssoUrl) {
                session(['moodle_sso_url' => $ssoUrl]);
            }
        }
    }
}
