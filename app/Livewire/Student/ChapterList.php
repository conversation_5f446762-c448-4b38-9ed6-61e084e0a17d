<?php

namespace App\Livewire\Student;

use Livewire\Component;
use App\Models\Course;
use App\Models\CourseChapter;
use App\Models\ChapterItem;
use App\Models\UserEngagementLog;
use App\Services\MoodleService;
use App\Services\MoodleSsoService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class ChapterList extends Component
{
    public $course;
    public $chapters;
    public $expandedChapter = 1; // First chapter expanded by default
    public $moodleCourseData = [];
    public $userProgress = [];

    protected $moodleService;
    protected $ssoService;

    public function boot(MoodleService $moodleService, MoodleSsoService $ssoService)
    {
        $this->moodleService = $moodleService;
        $this->ssoService = $ssoService;
    }

    public function mount()
    {
        $user = Auth::user();
        
        // Get the CTP course for the current user
        $enrollment = $user->courseEnrollments()->first();
        $this->course = $enrollment ? $enrollment->course : Course::where('title', 'like', '%CTP%')->first();
        
        if ($this->course) {
            $this->chapters = $this->course->chapters()->with('items')->orderBy('order')->get();
            
            // Load real Moodle course data if user has Moodle ID
            if ($user->moodle_user_id) {
                $this->loadMoodleCourseData($user);
            }
        }
    }

    private function loadMoodleCourseData($user)
    {
        try {
            $ctpCourseId = config('beaconlms.ctp_course.id');
            
            // Get course contents from Moodle
            $this->moodleCourseData = $this->moodleService->getCourseContents($ctpCourseId);
            
            // Get user progress from Moodle
            $this->userProgress = $this->moodleService->getActivitiesCompletion(
                $user->moodle_user_id,
                $ctpCourseId
            );
            
        } catch (\Exception $e) {
            Log::warning('Failed to load Moodle course data for chapter list', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    public function toggleChapter($chapterId)
    {
        $this->expandedChapter = $this->expandedChapter == $chapterId ? null : $chapterId;
        
        // Log engagement
        $this->logEngagement('chapter_view', $chapterId);
    }

    public function takeTest($chapterId)
    {
        // Log engagement for test attempt
        $this->logEngagement('quiz_attempt', $chapterId);

        // Redirect to Moodle for the specific chapter/quiz in new tab
        return $this->redirectToMoodleChapter($chapterId, 'quiz', true);
    }

    public function startChapter($chapterId)
    {
        // Log engagement for chapter start
        $this->logEngagement('chapter_start', $chapterId);

        // Redirect to Moodle for the specific chapter in new tab
        return $this->redirectToMoodleChapter($chapterId, null, true);
    }

    public function launchMoodleActivity($activityId, $activityType = 'resource')
    {
        $user = Auth::user();
        
        try {
            // Log engagement based on activity type
            $this->logEngagement($activityType . '_launch');
            
            // Generate SSO URL for specific activity
            $ssoUrl = $this->ssoService->generateSsoUrl($user);
            $moodleUrl = config('beaconlms.moodle.url');
            $activityUrl = rtrim($moodleUrl, '/') . '/mod/' . $activityType . '/view.php?id=' . $activityId;
            
            // Redirect to Moodle activity
            return redirect()->away($ssoUrl . '&wantsurl=' . urlencode($activityUrl));
            
        } catch (\Exception $e) {
            Log::error('Failed to launch Moodle activity', [
                'user_id' => $user->id,
                'activity_id' => $activityId,
                'error' => $e->getMessage()
            ]);
            
            session()->flash('error', 'Unable to launch activity. Please try again.');
        }
    }

    private function redirectToMoodleChapter($chapterId, $activityType = null, $newTab = false)
    {
        $user = Auth::user();
        
        try {
            // Find corresponding Moodle section/activity
            $moodleSection = $this->findMoodleSection($chapterId);
            
            if ($moodleSection) {
                $ssoUrl = $this->ssoService->generateSsoUrl($user);
                $ctpCourseId = config('beaconlms.ctp_course.id');
                $moodleUrl = config('beaconlms.moodle.url');
                
                if ($activityType === 'quiz' && !empty($moodleSection['modules'])) {
                    // Find quiz in the section
                    $quiz = collect($moodleSection['modules'])->firstWhere('modname', 'quiz');
                    if ($quiz) {
                        $targetUrl = rtrim($moodleUrl, '/') . '/mod/quiz/view.php?id=' . $quiz['id'];
                        $fullUrl = $ssoUrl . '&wantsurl=' . urlencode($targetUrl);
                        if ($newTab) {
                            return $this->js("window.open('{$fullUrl}', '_blank')");
                        } else {
                            return redirect()->away($fullUrl);
                        }
                    }
                }
                
                // Default to course section
                $targetUrl = rtrim($moodleUrl, '/') . '/course/view.php?id=' . $ctpCourseId . '#section-' . $moodleSection['section'];
                $fullUrl = $ssoUrl . '&wantsurl=' . urlencode($targetUrl);
                if ($newTab) {
                    return $this->js("window.open('{$fullUrl}', '_blank')");
                } else {
                    return redirect()->away($fullUrl);
                }
            }
            
            // Fallback to general course
            $fallbackUrl = route('student.launch.course');
            if ($newTab) {
                return $this->js("window.open('{$fallbackUrl}', '_blank')");
            } else {
                return redirect()->route('student.launch.course');
            }
            
        } catch (\Exception $e) {
            Log::error('Failed to redirect to Moodle chapter', [
                'user_id' => $user->id,
                'chapter_id' => $chapterId,
                'error' => $e->getMessage()
            ]);
            
            $fallbackUrl = route('student.launch.course');
            if ($newTab) {
                return $this->js("window.open('{$fallbackUrl}', '_blank')");
            } else {
                return redirect()->route('student.launch.course');
            }
        }
    }

    private function findMoodleSection($chapterId)
    {
        if (empty($this->moodleCourseData)) {
            return null;
        }
        
        // Try to match by chapter order/index
        $chapter = $this->chapters->find($chapterId);
        if ($chapter && isset($this->moodleCourseData[$chapter->order - 1])) {
            return $this->moodleCourseData[$chapter->order - 1];
        }
        
        return null;
    }

    private function logEngagement($activity, $chapterId = null)
    {
        try {
            $user = Auth::user();
            $course = $this->course;
            
            if ($course) {
                $studyTime = $this->getStudyTimeForActivity($activity);
                if ($studyTime > 0) {
                    UserEngagementLog::logStudyTime($user, $course, $studyTime);
                }
            }
            
        } catch (\Exception $e) {
            Log::warning('Failed to log engagement in chapter list', [
                'activity' => $activity,
                'chapter_id' => $chapterId,
                'error' => $e->getMessage()
            ]);
        }
    }

    private function getStudyTimeForActivity($activity): float
    {
        $activityTimes = [
            'chapter_view' => 0.1,      // 6 minutes
            'chapter_start' => 0.5,     // 30 minutes
            'quiz_attempt' => 1.0,      // 1 hour
            'resource_launch' => 0.25,  // 15 minutes
            'video_launch' => 0.5,      // 30 minutes
        ];

        return $activityTimes[$activity] ?? 0;
    }

    public function render()
    {
        return view('livewire.student.chapter-list');
    }
}
