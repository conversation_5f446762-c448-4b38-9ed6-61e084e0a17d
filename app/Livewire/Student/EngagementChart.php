<?php

namespace App\Livewire\Student;

use Livewire\Component;
use App\Models\UserEngagementLog;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class EngagementChart extends Component
{
    public $chartData;
    public $totalHours;

    public function mount()
    {
        $this->loadEngagementData();
    }

    public function loadEngagementData()
    {
        $user = Auth::user();
        
        // Get last 7 days of engagement data
        $startDate = Carbon::now()->subDays(6);
        $endDate = Carbon::now();
        
        $engagementLogs = UserEngagementLog::where('user_id', $user->id)
            ->whereBetween('date', [$startDate, $endDate])
            ->get()
            ->groupBy('date')
            ->map(function ($logs) {
                return $logs->sum('study_hours');
            });

        // Fill in missing days with 0
        $chartData = [];
        $labels = [];
        
        for ($date = $startDate->copy(); $date->lte($endDate); $date->addDay()) {
            $dateStr = $date->format('Y-m-d');
            $labels[] = $date->format('M j');
            $chartData[] = $engagementLogs->get($dateStr, 0);
        }

        // If no real data, use sample data for demo
        if (empty(array_filter($chartData))) {
            $chartData = [2, 4, 3, 5, 2, 6, 4]; // Sample hours
        }

        $this->chartData = [
            'labels' => $labels,
            'data' => $chartData
        ];

        $this->totalHours = array_sum($chartData);
    }

    public function render()
    {
        return view('livewire.student.engagement-chart');
    }
}
