<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ChapterItem extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'course_chapter_id',
        'title',
        'type',
        'moodle_activity_id',
        'xp_points',
        'is_completed',
        'order',
        'duration_minutes',
        'passing_score',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'xp_points' => 'integer',
        'is_completed' => 'boolean',
        'order' => 'integer',
        'duration_minutes' => 'integer',
        'passing_score' => 'integer',
    ];

    // ========================================
    // RELATIONSHIPS
    // ========================================

    /**
     * Get the chapter that owns the item
     */
    public function chapter(): BelongsTo
    {
        return $this->belongsTo(CourseChapter::class, 'course_chapter_id');
    }

    // ========================================
    // ACCESSOR METHODS
    // ========================================

    /**
     * Get formatted duration
     */
    public function getFormattedDurationAttribute(): ?string
    {
        if (!$this->duration_minutes) {
            return null;
        }

        $hours = floor($this->duration_minutes / 60);
        $minutes = $this->duration_minutes % 60;

        if ($hours > 0) {
            return $hours . 'h ' . $minutes . 'm';
        }

        return $minutes . 'm';
    }

    /**
     * Get type icon
     */
    public function getTypeIconAttribute(): string
    {
        return match ($this->type) {
            'video' => '🎥',
            'quiz' => '❓',
            'assignment' => '📝',
            'resource' => '📄',
            default => '📄',
        };
    }

    /**
     * Get type label
     */
    public function getTypeLabelAttribute(): string
    {
        return match ($this->type) {
            'video' => 'Video',
            'quiz' => 'Quiz',
            'assignment' => 'Assignment',
            'resource' => 'Resource',
            default => 'Resource',
        };
    }

    // ========================================
    // BUSINESS LOGIC METHODS
    // ========================================

    /**
     * Mark item as completed
     */
    public function markAsCompleted(): void
    {
        $this->update(['is_completed' => true]);
    }

    /**
     * Mark item as incomplete
     */
    public function markAsIncomplete(): void
    {
        $this->update(['is_completed' => false]);
    }

    /**
     * Check if item is a video
     */
    public function isVideo(): bool
    {
        return $this->type === 'video';
    }

    /**
     * Check if item is a quiz
     */
    public function isQuiz(): bool
    {
        return $this->type === 'quiz';
    }

    /**
     * Check if item is an assignment
     */
    public function isAssignment(): bool
    {
        return $this->type === 'assignment';
    }

    /**
     * Check if item is a resource
     */
    public function isResource(): bool
    {
        return $this->type === 'resource';
    }

    /**
     * Get XP points based on type if not set
     */
    public function getCalculatedXPPoints(): int
    {
        if ($this->xp_points > 0) {
            return $this->xp_points;
        }

        return match ($this->type) {
            'video' => config('beaconlms.xp_points.video_completion'),
            'quiz' => config('beaconlms.xp_points.quiz_passed'),
            'assignment' => config('beaconlms.xp_points.assignment_submitted'),
            default => 0,
        };
    }

    /**
     * Get next item in the chapter
     */
    public function getNextItem(): ?ChapterItem
    {
        return $this->chapter->items()
            ->where('order', $this->order + 1)
            ->first();
    }

    /**
     * Get previous item in the chapter
     */
    public function getPreviousItem(): ?ChapterItem
    {
        return $this->chapter->items()
            ->where('order', $this->order - 1)
            ->first();
    }

    /**
     * Check if item is unlocked for user
     */
    public function isUnlockedForUser(User $user): bool
    {
        // First item in chapter is unlocked if chapter is unlocked
        if ($this->order === 1) {
            return $this->chapter->isUnlockedForUser($user);
        }

        // Previous item must be completed
        $previousItem = $this->getPreviousItem();
        return $previousItem && $previousItem->is_completed;
    }

    // ========================================
    // SCOPE METHODS
    // ========================================

    /**
     * Scope to order items by order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('order');
    }

    /**
     * Scope to get completed items
     */
    public function scopeCompleted($query)
    {
        return $query->where('is_completed', true);
    }

    /**
     * Scope to get incomplete items
     */
    public function scopeIncomplete($query)
    {
        return $query->where('is_completed', false);
    }

    /**
     * Scope to get items by type
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope to get videos
     */
    public function scopeVideos($query)
    {
        return $query->where('type', 'video');
    }

    /**
     * Scope to get quizzes
     */
    public function scopeQuizzes($query)
    {
        return $query->where('type', 'quiz');
    }

    /**
     * Scope to get assignments
     */
    public function scopeAssignments($query)
    {
        return $query->where('type', 'assignment');
    }
}
