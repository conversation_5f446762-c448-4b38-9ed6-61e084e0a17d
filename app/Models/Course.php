<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Course extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'title',
        'description',
        'moodle_course_id',
        'thumbnail',
        'duration_days',
        'total_hours',
        'total_videos',
        'total_quizzes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'duration_days' => 'integer',
        'total_hours' => 'integer',
        'total_videos' => 'integer',
        'total_quizzes' => 'integer',
    ];

    // ========================================
    // RELATIONSHIPS
    // ========================================

    /**
     * Get the course enrollments
     */
    public function enrollments(): HasMany
    {
        return $this->hasMany(CourseEnrollment::class);
    }

    /**
     * Get the course chapters
     */
    public function chapters(): HasMany
    {
        return $this->hasMany(CourseChapter::class)->orderBy('order');
    }

    /**
     * Get the engagement logs for this course
     */
    public function engagementLogs(): HasMany
    {
        return $this->hasMany(UserEngagementLog::class);
    }

    // ========================================
    // ACCESSOR METHODS
    // ========================================

    /**
     * Get the thumbnail URL
     */
    public function getThumbnailUrlAttribute(): ?string
    {
        if (!$this->thumbnail) {
            return null;
        }

        return asset('storage/' . $this->thumbnail);
    }

    // ========================================
    // BUSINESS LOGIC METHODS
    // ========================================

    /**
     * Get total XP available in this course
     */
    public function getTotalXP(): int
    {
        return $this->chapters()
            ->with('items')
            ->get()
            ->flatMap->items
            ->sum('xp_points');
    }

    /**
     * Get total items count in this course
     */
    public function getTotalItemsCount(): int
    {
        return $this->chapters()
            ->with('items')
            ->get()
            ->flatMap->items
            ->count();
    }

    /**
     * Get completed items count for a user
     */
    public function getCompletedItemsCount(User $user): int
    {
        return $this->chapters()
            ->with('items')
            ->get()
            ->flatMap->items
            ->where('is_completed', true)
            ->count();
    }

    /**
     * Calculate progress percentage for a user
     */
    public function getProgressPercentage(User $user): float
    {
        $totalItems = $this->getTotalItemsCount();
        
        if ($totalItems === 0) {
            return 0.00;
        }

        $completedItems = $this->getCompletedItemsCount($user);
        
        return round(($completedItems / $totalItems) * 100, 2);
    }

    /**
     * Get enrolled students count
     */
    public function getEnrolledStudentsCount(): int
    {
        return $this->enrollments()->count();
    }

    /**
     * Get average progress across all enrolled students
     */
    public function getAverageProgress(): float
    {
        $enrollments = $this->enrollments()->get();
        
        if ($enrollments->isEmpty()) {
            return 0.00;
        }

        $totalProgress = $enrollments->sum('progress');
        
        return round($totalProgress / $enrollments->count(), 2);
    }

    /**
     * Check if course is the CTP Finance course
     */
    public function isCTPCourse(): bool
    {
        return $this->moodle_course_id === config('beaconlms.ctp_course.id');
    }

    /**
     * Get exam date for a user (from enrollment)
     */
    public function getExamDateForUser(User $user): ?string
    {
        $enrollment = $this->enrollments()
            ->where('user_id', $user->id)
            ->first();

        return $enrollment?->exam_date?->format('Y-m-d');
    }

    /**
     * Get days until exam for a user
     */
    public function getDaysUntilExam(User $user): ?int
    {
        $examDate = $this->getExamDateForUser($user);
        
        if (!$examDate) {
            return null;
        }

        return now()->diffInDays($examDate, false);
    }

    // ========================================
    // SCOPE METHODS
    // ========================================

    /**
     * Scope to get CTP course
     */
    public function scopeCTP($query)
    {
        return $query->where('moodle_course_id', config('beaconlms.ctp_course.id'));
    }

    /**
     * Scope to get courses with enrollments
     */
    public function scopeWithEnrollments($query)
    {
        return $query->has('enrollments');
    }
}
