<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class CourseChapter extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'course_id',
        'title',
        'description',
        'order',
        'moodle_section_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'order' => 'integer',
    ];

    // ========================================
    // RELATIONSHIPS
    // ========================================

    /**
     * Get the course that owns the chapter
     */
    public function course(): BelongsTo
    {
        return $this->belongsTo(Course::class);
    }

    /**
     * Get the chapter items
     */
    public function items(): HasMany
    {
        return $this->hasMany(ChapterItem::class)->orderBy('order');
    }

    // ========================================
    // BUSINESS LOGIC METHODS
    // ========================================

    /**
     * Get total XP available in this chapter
     */
    public function getTotalXP(): int
    {
        return $this->items()->sum('xp_points');
    }

    /**
     * Get completed items count for a user
     */
    public function getCompletedItemsCount(User $user): int
    {
        return $this->items()->where('is_completed', true)->count();
    }

    /**
     * Get total items count
     */
    public function getTotalItemsCount(): int
    {
        return $this->items()->count();
    }

    /**
     * Calculate progress percentage for a user
     */
    public function getProgressPercentage(User $user): float
    {
        $totalItems = $this->getTotalItemsCount();
        
        if ($totalItems === 0) {
            return 0.00;
        }

        $completedItems = $this->getCompletedItemsCount($user);
        
        return round(($completedItems / $totalItems) * 100, 2);
    }

    /**
     * Check if chapter is unlocked for a user
     */
    public function isUnlockedForUser(User $user): bool
    {
        // First chapter is always unlocked
        if ($this->order === 1) {
            return true;
        }

        // Get previous chapter
        $previousChapter = $this->course->chapters()
            ->where('order', $this->order - 1)
            ->first();

        if (!$previousChapter) {
            return true;
        }

        // Check if previous chapter meets unlock threshold
        $threshold = config('beaconlms.business_rules.chapter_unlock_threshold', 80);
        return $previousChapter->getProgressPercentage($user) >= $threshold;
    }

    /**
     * Check if chapter is completed for a user
     */
    public function isCompletedForUser(User $user): bool
    {
        return $this->getProgressPercentage($user) >= 100.00;
    }

    /**
     * Get XP earned by user in this chapter
     */
    public function getXPEarnedByUser(User $user): int
    {
        return $this->items()
            ->where('is_completed', true)
            ->sum('xp_points');
    }

    /**
     * Get next chapter
     */
    public function getNextChapter(): ?CourseChapter
    {
        return $this->course->chapters()
            ->where('order', $this->order + 1)
            ->first();
    }

    /**
     * Get previous chapter
     */
    public function getPreviousChapter(): ?CourseChapter
    {
        return $this->course->chapters()
            ->where('order', $this->order - 1)
            ->first();
    }

    // ========================================
    // SCOPE METHODS
    // ========================================

    /**
     * Scope to order chapters by order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('order');
    }

    /**
     * Scope to get unlocked chapters for a user
     */
    public function scopeUnlockedForUser($query, User $user)
    {
        return $query->get()->filter(function ($chapter) use ($user) {
            return $chapter->isUnlockedForUser($user);
        });
    }

    // ========================================
    // ACCESSORS
    // ========================================

    /**
     * Get progress percentage as an attribute
     */
    public function getProgressAttribute(): float
    {
        return $this->getProgressPercentage(auth()->user());
    }

    /**
     * Get description with fallback (only if null)
     */
    public function getDescriptionAttribute(): string
    {
        // Return the actual database value if it exists
        if (!empty($this->attributes['description'])) {
            return $this->attributes['description'];
        }

        // Only use this fallback if the DB value is truly empty/null
        return 'This chapter covers key concepts related to treasury management.';
    }
}
