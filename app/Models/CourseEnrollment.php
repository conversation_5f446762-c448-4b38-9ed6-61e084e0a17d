<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class CourseEnrollment extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'course_id',
        'moodle_enrollment_id',
        'progress',
        'enrolled_at',
        'completed_at',
        'exam_date',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'progress' => 'decimal:2',
        'enrolled_at' => 'datetime',
        'completed_at' => 'datetime',
        'exam_date' => 'date',
    ];

    // ========================================
    // RELATIONSHIPS
    // ========================================

    /**
     * Get the user that owns the enrollment
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the course for this enrollment
     */
    public function course(): BelongsTo
    {
        return $this->belongsTo(Course::class);
    }

    // ========================================
    // ACCESSOR METHODS
    // ========================================

    /**
     * Get days until exam
     */
    public function getDaysUntilExamAttribute(): ?int
    {
        if (!$this->exam_date) {
            return null;
        }

        return now()->diffInDays($this->exam_date, false);
    }

    /**
     * Get formatted exam date
     */
    public function getFormattedExamDateAttribute(): ?string
    {
        return $this->exam_date?->format('M j, Y');
    }

    /**
     * Check if enrollment is completed
     */
    public function getIsCompletedAttribute(): bool
    {
        return !is_null($this->completed_at);
    }

    /**
     * Get enrollment duration in days
     */
    public function getEnrollmentDurationAttribute(): int
    {
        $endDate = $this->completed_at ?? now();
        return $this->enrolled_at->diffInDays($endDate);
    }

    // ========================================
    // BUSINESS LOGIC METHODS
    // ========================================

    /**
     * Mark enrollment as completed
     */
    public function markAsCompleted(): void
    {
        $this->update([
            'completed_at' => now(),
            'progress' => 100.00,
        ]);
    }

    /**
     * Update progress percentage
     */
    public function updateProgress(float $progress): void
    {
        $this->update(['progress' => min(100.00, max(0.00, $progress))]);

        // Auto-complete if progress reaches 100%
        if ($progress >= 100.00 && !$this->completed_at) {
            $this->markAsCompleted();
        }
    }

    /**
     * Check if user can reschedule exam
     */
    public function canRescheduleExam(): bool
    {
        // Check if user hasn't exceeded max reschedules (this would need tracking)
        // For now, just check if exam date is in the future
        return $this->exam_date && $this->exam_date->isFuture();
    }

    /**
     * Reschedule exam date
     */
    public function rescheduleExam(Carbon $newDate): bool
    {
        if (!$this->canRescheduleExam()) {
            return false;
        }

        // Check minimum preparation period
        $minPrepDays = config('beaconlms.business_rules.minimum_preparation_days');
        if (now()->diffInDays($newDate) < $minPrepDays) {
            return false;
        }

        $this->update(['exam_date' => $newDate]);
        return true;
    }

    /**
     * Get XP earned from this enrollment
     */
    public function getXPEarned(): int
    {
        return $this->course->chapters()
            ->with('items')
            ->get()
            ->flatMap->items
            ->where('is_completed', true)
            ->sum('xp_points');
    }

    /**
     * Check if enrollment is eligible for certification
     */
    public function isEligibleForCertification(): bool
    {
        return $this->progress >= 100.00 && $this->getXPEarned() >= config('beaconlms.xp_points.certification_required');
    }

    // ========================================
    // SCOPE METHODS
    // ========================================

    /**
     * Scope to get active enrollments
     */
    public function scopeActive($query)
    {
        return $query->whereNull('completed_at');
    }

    /**
     * Scope to get completed enrollments
     */
    public function scopeCompleted($query)
    {
        return $query->whereNotNull('completed_at');
    }

    /**
     * Scope to get enrollments with upcoming exams
     */
    public function scopeUpcomingExams($query, int $days = 7)
    {
        return $query->whereBetween('exam_date', [now(), now()->addDays((int)$days)]);
    }
}
