<?php

namespace App\Models;


use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Carbon\Carbon;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'moodle_user_id',
        'role',
        'profile_photo',
        'current_streak',
        'last_activity_date',
        'last_synced_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'last_activity_date' => 'date',
            'last_synced_at' => 'datetime',
        ];
    }

    // ========================================
    // RELATIONSHIPS
    // ========================================

    /**
     * Get the user's course enrollments
     */
    public function courseEnrollments(): HasMany
    {
        return $this->hasMany(CourseEnrollment::class);
    }

    /**
     * Get the user's engagement logs
     */
    public function engagementLogs(): HasMany
    {
        return $this->hasMany(UserEngagementLog::class);
    }

    /**
     * Get the user's sessions for real-time tracking
     */
    public function userSessions(): HasMany
    {
        return $this->hasMany(UserSession::class);
    }

    // ========================================
    // ROLE METHODS
    // ========================================

    /**
     * Check if user has a specific role
     */
    public function hasRole(string $role): bool
    {
        return $this->role === $role;
    }

    /**
     * Check if user is admin
     */
    public function isAdmin(): bool
    {
        return $this->hasRole('admin');
    }

    /**
     * Check if user is student
     */
    public function isStudent(): bool
    {
        return $this->hasRole('student');
    }

    // ========================================
    // MOODLE INTEGRATION METHODS
    // ========================================

    /**
     * Check if user is synced with Moodle
     */
    public function isSyncedWithMoodle(): bool
    {
        return !is_null($this->moodle_user_id);
    }

    /**
     * Check if sync is recent (within 24 hours)
     */
    public function isSyncRecent(): bool
    {
        if (!$this->last_synced_at) {
            return false;
        }

        return $this->last_synced_at->diffInHours(now()) <= 24;
    }

    // ========================================
    // STREAK AND ENGAGEMENT METHODS
    // ========================================

    /**
     * Update user's activity streak
     */
    public function updateStreak(): void
    {
        $today = Carbon::today();
        $lastActivity = $this->last_activity_date ? Carbon::parse($this->last_activity_date) : null;

        if (!$lastActivity) {
            // First activity
            $this->update([
                'current_streak' => 1,
                'last_activity_date' => $today,
            ]);
            return;
        }

        $daysDiff = $lastActivity->diffInDays($today);

        if ($daysDiff === 0) {
            // Same day, no change to streak
            return;
        }

        if ($daysDiff === 1) {
            // Consecutive day, increment streak
            $streakIncrement = $this->isWeekend($today) ?
                config('beaconlms.engagement.weekend_streak_multiplier') : 1;

            $this->update([
                'current_streak' => $this->current_streak + $streakIncrement,
                'last_activity_date' => $today,
            ]);
        } else {
            // Streak broken, reset to 1
            $this->update([
                'current_streak' => 1,
                'last_activity_date' => $today,
            ]);
        }
    }

    /**
     * Check if date is weekend
     */
    private function isWeekend(Carbon $date): bool
    {
        return $date->isWeekend();
    }

    /**
     * Get total XP earned by user
     */
    public function getTotalXP(): int
    {
        return $this->courseEnrollments()
            ->with('course.chapters.items')
            ->get()
            ->sum(function ($enrollment) {
                return $enrollment->course->chapters
                    ->flatMap->items
                    ->where('is_completed', true)
                    ->sum('xp_points');
            });
    }

    /**
     * Get user's study hours for a specific period
     */
    public function getStudyHours(int $days = 7): array
    {
        $startDate = Carbon::today()->subDays($days - 1);

        $logs = $this->engagementLogs()
            ->where('date', '>=', $startDate)
            ->orderBy('date')
            ->get()
            ->keyBy('date');

        $studyHours = [];
        for ($i = 0; $i < $days; $i++) {
            $date = $startDate->copy()->addDays($i);
            $dateString = $date->toDateString();
            $studyHours[$dateString] = $logs->get($dateString)?->study_hours ?? 0;
        }

        return $studyHours;
    }

    /**
     * Get user's total study hours as a number
     */
    public function getTotalStudyHoursNumber(): float
    {
        return $this->engagementLogs()->sum('study_hours') ?: 26; // Default for demo
    }

    /**
     * Scope to add total XP to query
     */
    public function scopeWithTotalXP($query)
    {
        return $query->addSelect([
            'total_xp' => CourseEnrollment::selectRaw('COALESCE(SUM(
                (SELECT SUM(items.xp_points)
                 FROM chapters
                 INNER JOIN items ON chapters.id = items.chapter_id
                 WHERE chapters.course_id = course_enrollments.course_id
                 AND items.is_completed = true
                )), 0)')
            ])->leftJoin('course_enrollments', 'users.id', '=', 'course_enrollments.user_id');
    }

    /**
     * Get average study hours for all users
     */
    public static function avgStudyHours(): float
    {
        return self::join('user_engagement_logs', 'users.id', '=', 'user_engagement_logs.user_id')
                   ->avg('study_hours') ?? 0;
    }

    // ========================================
    // ACCESSOR METHODS
    // ========================================

    /**
     * Get the profile photo URL
     */
    public function getProfilePhotoUrlAttribute(): ?string
    {
        if (!$this->profile_photo) {
            return null;
        }

        return asset('storage/' . $this->profile_photo);
    }

    /**
     * Get user's first name
     */
    public function getFirstNameAttribute(): string
    {
        return explode(' ', $this->name)[0];
    }

    /**
     * Get user's last name
     */
    public function getLastNameAttribute(): string
    {
        $nameParts = explode(' ', $this->name, 2);
        return $nameParts[1] ?? '';
    }
}
