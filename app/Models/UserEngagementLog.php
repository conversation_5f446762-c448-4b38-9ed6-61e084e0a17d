<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class UserEngagementLog extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'course_id',
        'study_hours',
        'date',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'study_hours' => 'decimal:2',
        'date' => 'date',
    ];

    // ========================================
    // RELATIONSHIPS
    // ========================================

    /**
     * Get the user that owns the engagement log
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the course for this engagement log
     */
    public function course(): BelongsTo
    {
        return $this->belongsTo(Course::class);
    }

    // ========================================
    // BUSINESS LOGIC METHODS
    // ========================================

    /**
     * Add study time to the log
     */
    public function addStudyTime(float $hours): void
    {
        $this->update(['study_hours' => $this->study_hours + $hours]);
    }

    /**
     * Check if this log meets minimum daily activity
     */
    public function meetsMinimumActivity(): bool
    {
        $minimumMinutes = config('beaconlms.engagement.minimum_daily_activity_minutes');
        return ($this->study_hours * 60) >= $minimumMinutes;
    }

    /**
     * Get formatted study time
     */
    public function getFormattedStudyTimeAttribute(): string
    {
        $totalMinutes = $this->study_hours * 60;
        $hours = floor($totalMinutes / 60);
        $minutes = $totalMinutes % 60;

        if ($hours > 0) {
            return $hours . 'h ' . $minutes . 'm';
        }

        return $minutes . 'm';
    }

    /**
     * Check if log is for today
     */
    public function isToday(): bool
    {
        return $this->date->isToday();
    }

    /**
     * Check if log is for weekend
     */
    public function isWeekend(): bool
    {
        return $this->date->isWeekend();
    }

    // ========================================
    // STATIC METHODS
    // ========================================

    /**
     * Create or update engagement log for today
     */
    public static function logStudyTime(User $user, Course $course, float $hours): UserEngagementLog
    {
        $today = Carbon::today();

        $log = static::firstOrCreate([
            'user_id' => $user->id,
            'course_id' => $course->id,
            'date' => $today,
        ], [
            'study_hours' => 0,
        ]);

        $log->addStudyTime($hours);

        // Update user's streak if this meets minimum activity
        if ($log->meetsMinimumActivity()) {
            $user->updateStreak();
        }

        return $log;
    }

    /**
     * Get user's study hours for a date range
     */
    public static function getStudyHoursForPeriod(User $user, Carbon $startDate, Carbon $endDate): array
    {
        $logs = static::where('user_id', $user->id)
            ->whereBetween('date', [$startDate, $endDate])
            ->orderBy('date')
            ->get()
            ->keyBy(function ($log) {
                return $log->date->toDateString();
            });

        $studyHours = [];
        $currentDate = $startDate->copy();

        while ($currentDate->lte($endDate)) {
            $dateString = $currentDate->toDateString();
            $studyHours[$dateString] = $logs->get($dateString)?->study_hours ?? 0;
            $currentDate->addDay();
        }

        return $studyHours;
    }

    /**
     * Get total study hours for user
     */
    public static function getTotalStudyHours(User $user, Course $course = null): float
    {
        $query = static::where('user_id', $user->id);

        if ($course) {
            $query->where('course_id', $course->id);
        }

        return $query->sum('study_hours');
    }

    // ========================================
    // SCOPE METHODS
    // ========================================

    /**
     * Scope to get logs for today
     */
    public function scopeToday($query)
    {
        return $query->whereDate('date', Carbon::today());
    }

    /**
     * Scope to get logs for this week
     */
    public function scopeThisWeek($query)
    {
        return $query->whereBetween('date', [
            Carbon::now()->startOfWeek(),
            Carbon::now()->endOfWeek()
        ]);
    }

    /**
     * Scope to get logs for this month
     */
    public function scopeThisMonth($query)
    {
        return $query->whereMonth('date', Carbon::now()->month)
                    ->whereYear('date', Carbon::now()->year);
    }

    /**
     * Scope to get logs that meet minimum activity
     */
    public function scopeActiveStudy($query)
    {
        $minimumHours = config('beaconlms.engagement.minimum_daily_activity_minutes') / 60;
        return $query->where('study_hours', '>=', $minimumHours);
    }

    /**
     * Scope to get logs for a specific date range
     */
    public function scopeDateRange($query, Carbon $startDate, Carbon $endDate)
    {
        return $query->whereBetween('date', [$startDate, $endDate]);
    }
}
