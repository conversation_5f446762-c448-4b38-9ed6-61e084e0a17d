<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use App\Models\UserSession as UserSessionModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;


/**
 * User Session Model for Database-based Real-time Tracking
 *
 * Replaces Redis-based session tracking with database storage
 * for real-time dashboard features
 */
class UserSession extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'session_key',
        'activity_type',
        'started_at',
        'last_activity_at',
        'data',
        'is_active',
    ];

    protected $casts = [
        'started_at' => 'datetime',
        'last_activity_at' => 'datetime',
        'data' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Relationship with User
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope for active sessions
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)
                    ->where('last_activity_at', '>', now()->subMinutes(5));
    }

    /**
     * Scope for recent sessions
     */
    public function scopeRecent($query, int $minutes = 30)
    {
        return $query->where('last_activity_at', '>', now()->subMinutes($minutes));
    }

    /**
     * Start a new session for user
     */
    public static function startSession(User $user, string $activityType = 'dashboard', array $data = []): self
    {
        // End any existing active sessions for this user
        self::where('user_id', $user->id)
            ->where('is_active', true)
            ->update(['is_active' => false]);

        return self::create([
            'user_id' => $user->id,
            'session_key' => self::generateSessionKey($user->id),
            'activity_type' => $activityType,
            'started_at' => now(),
            'last_activity_at' => now(),
            'data' => $data,
            'is_active' => true,
        ]);
    }

    /**
     * Update session activity
     */
    public function updateActivity(string $activityType = null, array $data = []): void
    {
        $updateData = [
            'last_activity_at' => now(),
        ];

        if ($activityType) {
            $updateData['activity_type'] = $activityType;
        }

        if (!empty($data)) {
            $updateData['data'] = array_merge($this->data ?? [], $data);
        }

        $this->update($updateData);
    }

    /**
     * End the session
     */
    public function endSession(): void
    {
        $this->update(['is_active' => false]);
    }

    /**
     * Get session duration in minutes
     */
    public function getDurationInMinutes(): int
    {
        $endTime = $this->is_active ? now() : $this->updated_at;
        return $this->started_at->diffInMinutes($endTime);
    }

    /**
     * Get session duration in hours
     */
    public function getDurationInHours(): float
    {
        return $this->getDurationInMinutes() / 60;
    }

    /**
     * Check if session is still active (within 5 minutes)
     */
    public function isStillActive(): bool
    {
        return $this->is_active &&
               $this->last_activity_at->diffInMinutes(now()) <= 5;
    }

    /**
     * Generate unique session key
     */
    private static function generateSessionKey(int $userId): string
    {
        return 'session_' . $userId . '_' . now()->timestamp . '_' . uniqid();
    }

    /**
     * Get active session for user
     */
    public static function getActiveSession(User $user): ?self
    {
        return self::where('user_id', $user->id)
                   ->active()
                   ->latest('last_activity_at')
                   ->first();
    }

    /**
     * Clean up old inactive sessions
     */
    public static function cleanupOldSessions(int $daysOld = 7): int
    {
        return self::where('last_activity_at', '<', now()->subDays($daysOld))
                   ->delete();
    }

    /**
     * Get all active users with recent sessions
     */
    public static function getActiveUsers(): Collection
    {
        return User::whereHas('userSessions', function ($query) {
                $query->active();
            })
            ->with(['userSessions' => function ($query) {
                $query->active();
            }])
            ->get();
    }

    /**
     * Get count of active users
     */
    public static function getActiveUsersCount(): int
    {
        return self::active()->distinct('user_id')->count('user_id');
    }
}
