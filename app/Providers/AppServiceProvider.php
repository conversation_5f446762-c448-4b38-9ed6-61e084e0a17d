<?php

namespace App\Providers;

use App\Events\DataUpdatedEvent;
use App\Listeners\CacheInvalidationListener;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Event;
use Illuminate\Auth\Events\Login;
use App\Listeners\MoodleSsoListener;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Event::listen(Login::class, MoodleSsoListener::class);
        Event::listen(DataUpdatedEvent::class,CacheInvalidationListener::class );
    }
}
