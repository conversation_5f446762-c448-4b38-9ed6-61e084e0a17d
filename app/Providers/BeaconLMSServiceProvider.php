<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Route;
use App\Http\Middleware\RoleMiddleware;
use App\Http\Middleware\MoodleSyncMiddleware;

class BeaconLMSServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register BeaconLMS configuration
        $this->mergeConfigFrom(
            __DIR__.'/../../config/beaconlms.php', 'beaconlms'
        );

        // Register Moodle Service as singleton
        $this->app->singleton(\App\Services\MoodleService::class, function ($app) {
            return new \App\Services\MoodleService(
                config('beaconlms.moodle.url'),
                config('beaconlms.moodle.token'),
                config('beaconlms.moodle.service'),
                config('beaconlms.moodle.timeout')
            );
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Register middleware aliases
        Route::aliasMiddleware('role', RoleMiddleware::class);
        Route::aliasMiddleware('moodle.sync', MoodleSyncMiddleware::class);

        // Load routes (will be created in Phase 6)
        // $this->loadRoutesFrom(__DIR__.'/../../routes/student.php');
        
        // Load views (will be created in Phase 6)
        // $this->loadViewsFrom(__DIR__.'/../../resources/views/student', 'student');

        // Publish configuration
        $this->publishes([
            __DIR__.'/../../config/beaconlms.php' => config_path('beaconlms.php'),
        ], 'beaconlms-config');
    }
}
