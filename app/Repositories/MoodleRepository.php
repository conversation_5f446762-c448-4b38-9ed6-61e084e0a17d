<?php

namespace App\Repositories;

use App\Services\MoodleService;
use App\Exceptions\MoodleException;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Collection;

class MoodleRepository
{
    protected MoodleService $moodleService;

    public function __construct(MoodleService $moodleService)
    {
        $this->moodleService = $moodleService;
    }

    /**
     * Get user progress data for dashboard
     */
    public function getUserProgressData(int $moodleUserId, int $courseId): array
    {
        try {
            $completion = [];
            $activities = [];
            $grades = [];
            
            // Try to get course completion - handle gracefully if not configured
            try {
                $completion = $this->moodleService->getCourseCompletion($moodleUserId, $courseId);
            } catch (\Exception $e) {
                if (strpos($e->getMessage(), 'No completion criteria set') !== false) {
                    Log::warning('Course completion not configured, using fallback', [
                        'moodle_user_id' => $moodleUserId,
                        'course_id' => $courseId,
                        'message' => 'Course completion tracking not enabled in Moodle'
                    ]);
                    $completion = ['completions' => []]; // Empty completion data
                } else {
                    throw $e; // Re-throw if it's a different error
                }
            }
            
            // Try to get activities completion - handle gracefully if not available
            try {
                $activities = $this->moodleService->getActivitiesCompletion($moodleUserId, $courseId);
            } catch (\Exception $e) {
                Log::warning('Activities completion not available, using fallback', [
                    'moodle_user_id' => $moodleUserId,
                    'course_id' => $courseId,
                    'error' => $e->getMessage()
                ]);
                $activities = ['statuses' => []]; // Empty activities data
            }
            
            // Try to get user grades - handle gracefully if not available
            try {
                $grades = $this->moodleService->getUserGrades($moodleUserId, $courseId);
            } catch (\Exception $e) {
                Log::warning('User grades not available, using fallback', [
                    'moodle_user_id' => $moodleUserId,
                    'course_id' => $courseId,
                    'error' => $e->getMessage()
                ]);
                $grades = []; // Empty grades data
            }

            // Calculate progress with fallback methods
            $progressPercentage = $this->calculateProgressWithFallback($completion, $activities, $moodleUserId, $courseId);
            $xpEarned = $this->calculateXPEarned($activities);

            return [
                'completion' => $completion,
                'activities' => $activities,
                'grades' => $grades,
                'progress_percentage' => $progressPercentage,
                'xp_earned' => $xpEarned,
                'fallback_used' => empty($completion['completions']) && empty($activities['statuses']),
            ];

        } catch (\Exception $e) {
            Log::error('Failed to get user progress data', [
                'moodle_user_id' => $moodleUserId,
                'course_id' => $courseId,
                'error' => $e->getMessage()
            ]);

            // Return default data instead of throwing exception
            return [
                'completion' => ['completions' => []],
                'activities' => ['statuses' => []],
                'grades' => [],
                'progress_percentage' => 0.0,
                'xp_earned' => 0,
                'fallback_used' => true,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Sync course structure from Moodle
     */
    public function syncCourseStructure(int $moodleCourseId): array
    {
        try {
            $courseContents = $this->moodleService->getCourseContents($moodleCourseId);
            
            $structuredData = [];
            foreach ($courseContents as $section) {
                $chapter = [
                    'moodle_section_id' => $section['id'],
                    'title' => $section['name'],
                    'summary' => $section['summary'] ?? '',
                    'items' => []
                ];

                foreach ($section['modules'] ?? [] as $module) {
                    $item = [
                        'moodle_activity_id' => $module['id'],
                        'title' => $module['name'],
                        'type' => $this->mapMoodleModuleType($module['modname']),
                        'description' => $module['description'] ?? '',
                        'url' => $module['url'] ?? '',
                        'xp_points' => $this->calculateXPForActivity($module['modname']),
                    ];

                    $chapter['items'][] = $item;
                }

                $structuredData[] = $chapter;
            }

            return $structuredData;

        } catch (\Exception $e) {
            Log::error('Failed to sync course structure', [
                'moodle_course_id' => $moodleCourseId,
                'error' => $e->getMessage()
            ]);

            throw new MoodleException(
                'Failed to sync course structure',
                'course_structure_sync_failed',
                ['moodle_course_id' => $moodleCourseId]
            );
        }
    }

    /**
     * Get student engagement data for analytics
     */
    public function getStudentEngagementData(int $moodleUserId, int $courseId, int $days = 7): array
    {
        try {
            // This would typically require custom Moodle web services
            // For now, we'll return mock data structure
            $engagementData = [
                'daily_activity' => [],
                'total_time_spent' => 0,
                'activities_completed' => 0,
                'last_access' => null,
            ];

            // In a real implementation, you would call custom Moodle web services
            // to get detailed engagement metrics

            return $engagementData;

        } catch (\Exception $e) {
            Log::error('Failed to get engagement data', [
                'moodle_user_id' => $moodleUserId,
                'course_id' => $courseId,
                'error' => $e->getMessage()
            ]);

            return [
                'daily_activity' => [],
                'total_time_spent' => 0,
                'activities_completed' => 0,
                'last_access' => null,
            ];
        }
    }

    /**
     * Batch sync multiple users
     */
    public function batchSyncUsers(Collection $users): array
    {
        $results = [
            'success' => [],
            'failed' => [],
        ];

        foreach ($users as $user) {
            try {
                if (!$user->moodle_user_id) {
                    // Create user in Moodle
                    $moodleUsers = $this->moodleService->createUsers([
                        [
                            'username' => $this->generateUsername($user->email),
                            'password' => \Illuminate\Support\Str::random(12),
                            'firstname' => explode(' ', $user->name)[0],
                            'lastname' => explode(' ', $user->name, 2)[1] ?? '',
                            'email' => $user->email,
                        ]
                    ]);

                    if (!empty($moodleUsers)) {
                        $user->update(['moodle_user_id' => $moodleUsers[0]['id']]);
                        $results['success'][] = $user->id;
                    } else {
                        $results['failed'][] = ['user_id' => $user->id, 'error' => 'Failed to create in Moodle'];
                    }
                } else {
                    $results['success'][] = $user->id;
                }

            } catch (\Exception $e) {
                $results['failed'][] = [
                    'user_id' => $user->id,
                    'error' => $e->getMessage()
                ];
            }
        }

        return $results;
    }

    /**
     * Calculate progress percentage from completion data
     */
    private function calculateProgressPercentage(array $completion): float
    {
        if (empty($completion['completions'])) {
            return 0.00;
        }

        $total = count($completion['completions']);
        $completed = collect($completion['completions'])
            ->where('state', 1)
            ->count();

        return $total > 0 ? round(($completed / $total) * 100, 2) : 0.00;
    }

    /**
     * Calculate progress with fallback methods when completion tracking is not available
     */
    private function calculateProgressWithFallback(array $completion, array $activities, int $moodleUserId, int $courseId): float
    {
        // Try standard completion calculation first
        if (!empty($completion['completions'])) {
            return $this->calculateProgressPercentage($completion);
        }

        // Fallback 1: Use activities completion if available
        if (!empty($activities['statuses'])) {
            $total = count($activities['statuses']);
            $completed = collect($activities['statuses'])
                ->where('state', 1)
                ->count();
            
            $progress = $total > 0 ? round(($completed / $total) * 100, 2) : 0.00;
            
            Log::info('Using activities-based progress calculation', [
                'moodle_user_id' => $moodleUserId,
                'course_id' => $courseId,
                'total_activities' => $total,
                'completed_activities' => $completed,
                'progress' => $progress
            ]);
            
            return $progress;
        }

        // Fallback 2: Try to get course contents and estimate progress
        try {
            $courseContents = $this->moodleService->getCourseContents($courseId);
            $totalActivities = 0;
            
            foreach ($courseContents as $section) {
                if (!empty($section['modules'])) {
                    $totalActivities += count($section['modules']);
                }
            }
            
            if ($totalActivities > 0) {
                // Since we can't get completion status, assume 0% progress for new enrollments
                // This will be updated as the student progresses
                Log::info('Using course structure-based progress estimation', [
                    'moodle_user_id' => $moodleUserId,
                    'course_id' => $courseId,
                    'total_activities' => $totalActivities,
                    'estimated_progress' => 0.0
                ]);
                
                return 0.0; // New enrollment, no progress yet
            }
        } catch (\Exception $e) {
            Log::warning('Could not estimate progress from course structure', [
                'moodle_user_id' => $moodleUserId,
                'course_id' => $courseId,
                'error' => $e->getMessage()
            ]);
        }

        // Fallback 3: Return 0% progress
        Log::info('Using default progress (0%) - no completion data available', [
            'moodle_user_id' => $moodleUserId,
            'course_id' => $courseId
        ]);
        
        return 0.0;
    }

    /**
     * Calculate XP earned from activities
     */
    private function calculateXPEarned(array $activities): int
    {
        $totalXP = 0;

        foreach ($activities['statuses'] ?? [] as $activity) {
            if ($activity['state'] == 1) { // Completed
                $totalXP += $this->getXPForActivityType($activity['modname'] ?? 'unknown');
            }
        }

        return $totalXP;
    }

    /**
     * Map Moodle module type to our internal types
     */
    private function mapMoodleModuleType(string $modname): string
    {
        $mapping = [
            'quiz' => 'quiz',
            'assign' => 'assignment',
            'resource' => 'resource',
            'url' => 'resource',
            'page' => 'resource',
            'book' => 'resource',
            'scorm' => 'video',
            'h5pactivity' => 'video',
        ];

        return $mapping[$modname] ?? 'resource';
    }

    /**
     * Calculate XP points for activity type
     */
    private function calculateXPForActivity(string $modname): int
    {
        $xpMapping = [
            'quiz' => config('beaconlms.xp_points.quiz_passed'),
            'assign' => config('beaconlms.xp_points.assignment_submitted'),
            'scorm' => config('beaconlms.xp_points.video_completion'),
            'h5pactivity' => config('beaconlms.xp_points.video_completion'),
        ];

        return $xpMapping[$modname] ?? 0;
    }

    /**
     * Get XP for activity type
     */
    private function getXPForActivityType(string $modname): int
    {
        return $this->calculateXPForActivity($modname);
    }

    /**
     * Generate username from email
     */
    private function generateUsername(string $email): string
    {
        return \Illuminate\Support\Str::slug(explode('@', $email)[0]);
    }
}
