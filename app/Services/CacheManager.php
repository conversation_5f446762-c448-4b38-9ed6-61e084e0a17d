<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\User;
use App\Models\UserSession;
use Carbon\Carbon;

/**
 * Advanced Cache Manager for BeaconLMS (Database Cache Driver)
 *
 * Implements intelligent caching with:
 * - Multi-layer caching (L1: Memory, L2: Database Cache, L3: Database)
 * - Smart invalidation strategies with database-based tagging
 * - Real-time data consistency using database sessions
 * - Performance optimization for database cache driver
 */
class CacheManager
{
    // Cache TTL Constants (in seconds)
    private const CACHE_TTL_SHORT = 10;        // 1 minute - Real-time data
    private const CACHE_TTL_MEDIUM = 300;      // 5 minutes - Semi-static data
    private const CACHE_TTL_LONG = 3600;       // 1 hour - Static data
    private const CACHE_TTL_VERY_LONG = 86400; // 24 hours - Configuration data

    // Cache Tags for Selective Invalidation
    private const TAG_USER_STATS = 'user_stats';
    private const TAG_DASHBOARD = 'dashboard';
    private const TAG_PROGRESS = 'progress';
    private const TAG_ENGAGEMENT = 'engagement';
    private const TAG_MOODLE = 'moodle';
    private const TAG_EXAM = 'exam';

    /**
     * Get cached data with intelligent fallback (Database Cache Compatible)
     */
    public function remember(string $key, int $ttl, callable $callback, array $tags = []): mixed
    {
        // Try L1 Cache (Memory) first
        $memoryKey = "memory:{$key}";
        if ($this->hasMemoryCache($memoryKey)) {
            return $this->getMemoryCache($memoryKey);
        }

        // Try L2 Cache (Database) with custom tagging
        $cacheKey = $this->buildCacheKey($key);

        $result = Cache::get($cacheKey);

        if ($result !== null) {
            // Cache hit
            $this->recordCacheHit(true);
            $this->setMemoryCache($memoryKey, $result, min(60, $ttl));
            return $result;
        }

        // Cache miss - calculate and store
        $this->recordCacheHit(false);

        return Cache::remember($cacheKey, $ttl, function () use ($callback, $memoryKey, $key, $tags, $ttl) {
            $data = $callback();

            // Store in L1 Cache for immediate access
            $this->setMemoryCache($memoryKey, $data, min(60, $ttl));

            // Store cache tags in database for selective invalidation
            $this->storeCacheTags($key, $tags, $ttl);

            return $data;
        });
    }

    /**
     * Smart cache invalidation based on data type and user
     */
    public function invalidateUserCache(User $user, array $dataTypes = []): void
    {
        $userId = $user->id;

        // Default to invalidating all user-related caches
        if (empty($dataTypes)) {
            $dataTypes = ['stats', 'dashboard', 'progress', 'engagement', 'exam'];
        }

        foreach ($dataTypes as $type) {
            $this->invalidateByType($userId, $type);
        }

        // Clear memory cache for this user
        $this->clearUserMemoryCache($userId);

        Log::info('Cache invalidated for user', [
            'user_id' => $userId,
            'data_types' => $dataTypes,
            'timestamp' => now()
        ]);
    }

    /**
     * Instant cache invalidation for critical updates (synchronous)
     * Used for exam date changes that need immediate reflection
     */
    public function invalidateUserCacheInstant(User $user, array $dataTypes = []): void
    {
        $userId = $user->id;

        // Default to invalidating critical caches
        if (empty($dataTypes)) {
            $dataTypes = ['exam', 'stats', 'dashboard'];
        }

        // Clear Laravel cache immediately
        foreach ($dataTypes as $type) {
            $this->invalidateByType($userId, $type);
        }

        // Clear memory cache immediately
        $this->clearUserMemoryCache($userId);

        // Clean up expired tags immediately
        $this->cleanupExpiredTags();

        Log::info('Instant cache invalidation completed', [
            'user_id' => $userId,
            'data_types' => $dataTypes,
            'timestamp' => now()
        ]);
    }

    /**
     * Get dashboard stats with intelligent caching
     */
    public function getDashboardStats(User $user, ?array $moodleCourses = null): array
    {
        $cacheKey = "dashboard_stats_{$user->id}";
        $tags = [self::TAG_USER_STATS, self::TAG_DASHBOARD, "user_{$user->id}"];

        // Use shorter TTL if user has recent activity
        $ttl = $this->hasRecentActivity($user) ? self::CACHE_TTL_SHORT : self::CACHE_TTL_MEDIUM;

        return $this->remember($cacheKey, $ttl, function () use ($user, $moodleCourses) {
            // This would call the actual stats calculation
            return $this->calculateFreshStats($user, $moodleCourses);
        }, $tags);
    }

    /**
     * Cache user progress with versioning
     */
    public function getUserProgress(User $user): array
    {
        $version = $this->getUserDataVersion($user);
        $cacheKey = "user_progress_{$user->id}_v{$version}";
        $tags = [self::TAG_PROGRESS, "user_{$user->id}"];

        return $this->remember($cacheKey, self::CACHE_TTL_MEDIUM, function () use ($user) {
            return $this->calculateUserProgress($user);
        }, $tags);
    }

    /**
     * Cache Moodle data with shorter TTL for real-time sync
     */
    public function getMoodleData(User $user, string $dataType): mixed
    {
        $cacheKey = "moodle_{$dataType}_{$user->id}";
        $tags = [self::TAG_MOODLE, "user_{$user->id}"];

        // Moodle data should be fresher
        return $this->remember($cacheKey, self::CACHE_TTL_SHORT, function () use ($user, $dataType) {
            return $this->fetchMoodleData($user, $dataType);
        }, $tags);
    }

    /**
     * Warm cache for frequently accessed data
     */
    public function warmCache(User $user): void
    {
        // Pre-load dashboard stats
        $this->getDashboardStats($user);

        // Pre-load user progress
        $this->getUserProgress($user);

        // Pre-load engagement data
        $this->warmEngagementCache($user);

        Log::info('Cache warmed for user', ['user_id' => $user->id]);
    }

    /**
     * Event-driven cache invalidation
     */
    public function handleDataUpdate(string $eventType, array $data): void
    {
        switch ($eventType) {
            case 'progress_updated':
                $this->invalidateUserCache(User::find($data['user_id']), ['progress', 'stats']);
                break;

            case 'engagement_logged':
                $this->invalidateUserCache(User::find($data['user_id']), ['engagement', 'stats']);
                break;

            case 'exam_date_changed':
                // Use instant invalidation for exam date changes
                $user = User::find($data['user_id']);
                if ($user) {
                    $this->invalidateUserCacheInstant($user, ['exam', 'stats', 'dashboard']);
                }
                break;

            case 'moodle_sync_completed':
                $this->invalidateUserCache(User::find($data['user_id']), ['moodle', 'stats', 'progress']);
                break;
        }
    }

    /**
     * Get cache statistics for monitoring (Database Cache)
     */
    public function getCacheStats(): array
    {
        return [
            'memory_usage' => $this->getMemoryCacheStats(),
            'database_cache_stats' => $this->getDatabaseCacheStats(),
            'hit_rate' => $this->calculateHitRate(),
            'active_keys' => $this->getActiveKeyCount(),
            'cache_driver' => 'database',
        ];
    }

    /**
     * Build standardized cache key
     */
    private function buildCacheKey(string $key): string
    {
        return "beaconlms:{$key}:" . config('app.env');
    }

    /**
     * Check if user has recent activity (affects cache TTL)
     */
    private function hasRecentActivity(User $user): bool
    {
        $lastActivity = Cache::get("last_activity_{$user->id}");
        return $lastActivity && Carbon::parse($lastActivity)->diffInMinutes(now()) < 5;
    }

    /**
     * Get user data version for cache versioning
     */
    private function getUserDataVersion(User $user): string
    {
        // Combine timestamps of last updates to create version
        $lastSync = $user->last_synced_at?->timestamp ?? 0;
        $lastUpdate = $user->updated_at->timestamp;

        return md5($lastSync . $lastUpdate);
    }

    /**
     * Invalidate cache by type
     */
    private function invalidateByType(int $userId, string $type): void
    {
        $patterns = [
            'stats' => ["dashboard_stats_{$userId}", "user_stats_{$userId}"],
            'progress' => ["user_progress_{$userId}_*", "progress_*_{$userId}"],
            'engagement' => ["engagement_*_{$userId}", "activity_*_{$userId}"],
            'exam' => ["exam_*_{$userId}"],
            'moodle' => ["moodle_*_{$userId}"],
        ];

        if (isset($patterns[$type])) {
            foreach ($patterns[$type] as $pattern) {
                $this->forgetPattern($pattern);
            }
        }

        // Clear tagged cache using database-based tagging
        $tagMap = [
            'stats' => self::TAG_USER_STATS,
            'progress' => self::TAG_PROGRESS,
            'engagement' => self::TAG_ENGAGEMENT,
            'exam' => self::TAG_EXAM,
            'moodle' => self::TAG_MOODLE,
        ];

        if (isset($tagMap[$type])) {
            $this->clearCacheByTags([$tagMap[$type], "user_{$userId}"]);
        }
    }

    /**
     * Memory cache operations (L1 Cache)
     */
    private function hasMemoryCache(string $key): bool
    {
        return isset($GLOBALS['memory_cache'][$key]) &&
               $GLOBALS['memory_cache'][$key]['expires'] > time();
    }

    private function getMemoryCache(string $key): mixed
    {
        return $GLOBALS['memory_cache'][$key]['data'] ?? null;
    }

    private function setMemoryCache(string $key, mixed $data, int $ttl): void
    {
        $GLOBALS['memory_cache'][$key] = [
            'data' => $data,
            'expires' => time() + $ttl
        ];
    }

    private function clearUserMemoryCache(int $userId): void
    {
        if (!isset($GLOBALS['memory_cache'])) return;

        foreach (array_keys($GLOBALS['memory_cache']) as $key) {
            if (str_contains($key, "_{$userId}")) {
                unset($GLOBALS['memory_cache'][$key]);
            }
        }
    }

    /**
     * Pattern-based cache invalidation for database cache
     */
    private function forgetPattern(string $pattern): void
    {
        if (str_contains($pattern, '*')) {
            // Use database query for pattern matching
            $likePattern = str_replace('*', '%', $this->buildCacheKey($pattern));

            DB::table('cache')
                ->where('key', 'like', $likePattern)
                ->delete();
        } else {
            Cache::forget($this->buildCacheKey($pattern));
        }
    }

    /**
     * Record cache hit/miss for statistics
     */
    private function recordCacheHit(bool $hit): void
    {
        try {
            DB::table('cache_statistics')->updateOrInsert(
                [
                    'date' => now()->format('Y-m-d'),
                    'hour' => now()->hour,
                ],
                [
                    'hits' => DB::raw($hit ? 'hits + 1' : 'hits'),
                    'misses' => DB::raw($hit ? 'misses' : 'misses + 1'),
                    'updated_at' => now(),
                ]
            );
        } catch (\Exception $e) {
            // Silently fail to avoid breaking cache operations
            Log::debug('Failed to record cache statistics', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Placeholder methods for actual data calculation
     */
    private function calculateFreshStats(User $user, ?array $moodleCourses): array
    {
        // This would integrate with DashboardStatsService
        return [];
    }

    private function calculateUserProgress(User $user): array
    {
        // This would integrate with UserProgressCalculator
        return [];
    }

    private function fetchMoodleData(User $user, string $dataType): mixed
    {
        // This would integrate with MoodleService
        return null;
    }

    private function warmEngagementCache(User $user): void
    {
        // Pre-load engagement data
    }

    private function getMemoryCacheStats(): array
    {
        return [
            'keys' => count($GLOBALS['memory_cache'] ?? []),
            'memory_usage' => memory_get_usage(true)
        ];
    }

    private function getDatabaseCacheStats(): array
    {
        return [
            'total_entries' => DB::table('cache')->count(),
            'expired_entries' => DB::table('cache')->where('expiration', '<', now()->timestamp)->count(),
            'active_entries' => DB::table('cache')->where('expiration', '>=', now()->timestamp)->count(),
            'cache_size_mb' => DB::table('cache')->sum(DB::raw('LENGTH(value)')) / 1024 / 1024,
        ];
    }

    private function calculateHitRate(): float
    {
        // Get hit rate from cache statistics table if exists
        $stats = DB::table('cache_statistics')
            ->where('created_at', '>=', now()->subHour())
            ->selectRaw('SUM(hits) as total_hits, SUM(misses) as total_misses')
            ->first();

        if (!$stats || ($stats->total_hits + $stats->total_misses) === 0) {
            return 0.0;
        }

        return ($stats->total_hits / ($stats->total_hits + $stats->total_misses)) * 100;
    }

    private function getActiveKeyCount(): int
    {
        return DB::table('cache')->where('expiration', '>=', now()->timestamp)->count();
    }

    /**
     * Store cache tags in database for selective invalidation
     */
    private function storeCacheTags(string $key, array $tags, int $ttl): void
    {
        if (empty($tags)) {
            return;
        }

        $expiresAt = now()->addSeconds($ttl);
        $cacheKey = $this->buildCacheKey($key);

        foreach ($tags as $tag) {
            DB::table('cache_tags')->updateOrInsert(
                [
                    'tag' => $tag,
                    'cache_key' => $cacheKey,
                ],
                [
                    'expires_at' => $expiresAt,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]
            );
        }
    }

    /**
     * Clear cache by tags (database implementation)
     */
    private function clearCacheByTags(array $tags): void
    {
        if (empty($tags)) {
            return;
        }

        // Get all cache keys associated with these tags
        $cacheKeys = DB::table('cache_tags')
            ->whereIn('tag', $tags)
            ->where('expires_at', '>', now())
            ->pluck('cache_key')
            ->unique();

        // Delete cache entries
        if ($cacheKeys->isNotEmpty()) {
            DB::table('cache')
                ->whereIn('key', $cacheKeys)
                ->delete();

            // Clean up cache tags
            DB::table('cache_tags')
                ->whereIn('cache_key', $cacheKeys)
                ->delete();
        }
    }

    /**
     * Clean up expired cache tags
     */
    public function cleanupExpiredTags(): void
    {
        DB::table('cache_tags')
            ->where('expires_at', '<', now())
            ->delete();
    }
}
