<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\User;
use Carbon\Carbon;

/**
 * Simplified Cache Manager for BeaconLMS
 *
 * Provides intelligent database caching with:
 * - Activity-based TTL optimization
 * - Tag-based selective invalidation
 * - Performance monitoring and statistics
 */
class CacheManager
{
    // Cache TTL Constants (in seconds)
    private const CACHE_TTL_SHORT = 60;       // 1 minute - Active users
    private const CACHE_TTL_MEDIUM = 300;     // 5 minutes - Standard data
    private const CACHE_TTL_LONG = 3600;      // 1 hour - Static data

    // Cache Tags for Selective Invalidation
    private const TAG_USER_STATS = 'user_stats';
    private const TAG_DASHBOARD = 'dashboard';
    private const TAG_PROGRESS = 'progress';
    private const TAG_ENGAGEMENT = 'engagement';
    private const TAG_MOODLE = 'moodle';
    private const TAG_EXAM = 'exam';

    /**
     * Get cached data with intelligent caching and tagging
     */
    public function remember(string $key, int $ttl, callable $callback, array $tags = []): mixed
    {
        $cacheKey = $this->buildCacheKey($key);

        $result = Cache::get($cacheKey);

        if ($result !== null) {
            $this->recordCacheHit(true);
            return $result;
        }

        // Cache miss - calculate and store
        $this->recordCacheHit(false);

        return Cache::remember($cacheKey, $ttl, function () use ($callback, $key, $tags, $ttl) {
            $data = $callback();

            // Store cache tags for selective invalidation
            if (!empty($tags)) {
                $this->storeCacheTags($key, $tags, $ttl);
            }

            return $data;
        });
    }

    /**
     * Invalidate user cache by data types
     */
    public function invalidateUserCache(User $user, array $dataTypes = []): void
    {
        $userId = $user->id;

        // Default to invalidating all user-related caches
        if (empty($dataTypes)) {
            $dataTypes = ['stats', 'dashboard', 'progress', 'engagement', 'exam'];
        }

        foreach ($dataTypes as $type) {
            $this->invalidateByType($userId, $type);
        }

        Log::info('Cache invalidated for user', [
            'user_id' => $userId,
            'data_types' => $dataTypes,
            'timestamp' => now()
        ]);
    }

    /**
     * Warm cache for frequently accessed data
     */
    public function warmCache(User $user): void
    {
        Log::info('Cache warming initiated for user', ['user_id' => $user->id]);
        
        // Cache warming will be implemented by calling services that use this CacheManager
        // This method serves as a placeholder for external cache warming operations
    }

    /**
     * Event-driven cache invalidation
     */
    public function handleDataUpdate(string $eventType, array $data): void
    {
        $user = User::find($data['user_id'] ?? null);
        if (!$user) {
            return;
        }

        $invalidationMap = [
            'progress_updated' => ['progress', 'stats'],
            'engagement_logged' => ['engagement', 'stats'],
            'exam_date_changed' => ['exam', 'stats', 'dashboard'],
            'moodle_sync_completed' => ['moodle', 'stats', 'progress'],
        ];

        if (isset($invalidationMap[$eventType])) {
            $this->invalidateUserCache($user, $invalidationMap[$eventType]);
        }
    }

    /**
     * Get cache statistics for monitoring
     */
    public function getCacheStats(): array
    {
        return [
            'database_cache_stats' => $this->getDatabaseCacheStats(),
            'hit_rate' => $this->calculateHitRate(),
            'active_keys' => $this->getActiveKeyCount(),
            'cache_driver' => 'database',
        ];
    }

    /**
     * Build standardized cache key
     */
    private function buildCacheKey(string $key): string
    {
        return "beaconlms:{$key}:" . config('app.env');
    }

    /**
     * Check if user has recent activity (affects cache TTL)
     */
    private function hasRecentActivity(User $user): bool
    {
        $lastActivity = $user->engagementLogs()->latest()->first();
        return $lastActivity && $lastActivity->created_at->diffInMinutes(now()) < 10;
    }

    /**
     * Invalidate cache by type and user
     */
    private function invalidateByType(int $userId, string $type): void
    {
        // Map cache types to their corresponding tags
        $tagMap = [
            'stats' => self::TAG_USER_STATS,
            'dashboard' => self::TAG_DASHBOARD,
            'progress' => self::TAG_PROGRESS,
            'engagement' => self::TAG_ENGAGEMENT,
            'exam' => self::TAG_EXAM,
            'moodle' => self::TAG_MOODLE,
        ];

        if (isset($tagMap[$type])) {
            $this->clearCacheByTags([$tagMap[$type], "user_{$userId}"]);
        }
    }


    /**
     * Record cache hit/miss for statistics
     */
    private function recordCacheHit(bool $hit): void
    {
        try {
            DB::table('cache_statistics')->updateOrInsert(
                [
                    'date' => now()->format('Y-m-d'),
                    'hour' => now()->hour,
                ],
                [
                    'hits' => DB::raw($hit ? 'hits + 1' : 'hits'),
                    'misses' => DB::raw($hit ? 'misses' : 'misses + 1'),
                    'updated_at' => now(),
                ]
            );
        } catch (\Exception $e) {
            // Silently fail to avoid breaking cache operations
            Log::debug('Failed to record cache statistics', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Get database cache statistics
     */
    private function getDatabaseCacheStats(): array
    {
        return [
            'total_entries' => DB::table('cache')->count(),
            'expired_entries' => DB::table('cache')->where('expiration', '<', now()->timestamp)->count(),
            'active_entries' => DB::table('cache')->where('expiration', '>=', now()->timestamp)->count(),
            'cache_size_mb' => round(DB::table('cache')->sum(DB::raw('LENGTH(value)')) / 1024 / 1024, 2),
        ];
    }

    /**
     * Calculate cache hit rate percentage
     */
    private function calculateHitRate(): float
    {
        $stats = DB::table('cache_statistics')
            ->where('date', '>=', now()->subDay()->format('Y-m-d'))
            ->selectRaw('SUM(hits) as total_hits, SUM(misses) as total_misses')
            ->first();

        if (!$stats || ($stats->total_hits + $stats->total_misses) === 0) {
            return 0.0;
        }

        return round(($stats->total_hits / ($stats->total_hits + $stats->total_misses)) * 100, 2);
    }

    /**
     * Get count of active cache keys
     */
    private function getActiveKeyCount(): int
    {
        return DB::table('cache')->where('expiration', '>=', now()->timestamp)->count();
    }

    /**
     * Store cache tags in database for selective invalidation
     */
    private function storeCacheTags(string $key, array $tags, int $ttl): void
    {
        if (empty($tags)) {
            return;
        }

        $expiresAt = now()->addSeconds($ttl);
        $cacheKey = $this->buildCacheKey($key);

        foreach ($tags as $tag) {
            DB::table('cache_tags')->updateOrInsert(
                [
                    'tag' => $tag,
                    'cache_key' => $cacheKey,
                ],
                [
                    'expires_at' => $expiresAt,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]
            );
        }
    }

    /**
     * Clear cache by tags (database implementation)
     */
    private function clearCacheByTags(array $tags): void
    {
        if (empty($tags)) {
            return;
        }

        // Get all cache keys associated with these tags
        $cacheKeys = DB::table('cache_tags')
            ->whereIn('tag', $tags)
            ->where('expires_at', '>', now())
            ->pluck('cache_key')
            ->unique();

        // Delete cache entries and associated tags
        if ($cacheKeys->isNotEmpty()) {
            DB::table('cache')->whereIn('key', $cacheKeys)->delete();
            DB::table('cache_tags')->whereIn('cache_key', $cacheKeys)->delete();
        }
    }

    /**
     * Clean up expired cache tags
     */
    public function cleanupExpiredTags(): void
    {
        DB::table('cache_tags')
            ->where('expires_at', '<', now())
            ->delete();
    }
}
