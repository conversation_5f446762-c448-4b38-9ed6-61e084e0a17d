<?php

namespace App\Services;

use App\Models\User;
use App\Models\Course;
use App\Models\CourseEnrollment;
use App\Models\UserEngagementLog;
use App\Repositories\MoodleRepository;
use App\Services\CacheManager;
use App\Events\DataUpdatedEvent;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

/**
 * Service responsible for calculating and managing dashboard statistics
 * 
 * This service handles all dashboard-related calculations including:
 * - User progress statistics
 * - Study time tracking
 * - Course completion metrics
 * - Exam countdown calculations
 */
class DashboardStatsService
{
    private const CACHE_TTL = 300; // 5 minutes (default)
    private const CACHE_TTL_ACTIVE = 60;   // 1 minute for active users
    private const CACHE_TTL_RECENT = 180;  // 3 minutes for recently active users
    private const DEFAULT_EXAM_PREPARATION_DAYS = 43;
    private const HOURS_PER_DAY = 12;

    public function __construct(
        private readonly MoodleRepository $moodleRepository,
        private readonly UserProgressCalculator $progressCalculator,
        private readonly ExamDateManager $examDateManager,
        private readonly EngagementTracker $engagementTracker,
        private readonly CacheManager $cacheManager
    ) {}

    /**
     * Get comprehensive dashboard statistics for a user with intelligent caching
     */
    public function getDashboardStats(User $user, ?array $moodleCourses = null): array
    {
        // Use advanced caching with real-time invalidation
        return $this->cacheManager->remember(
            "dashboard_stats_{$user->id}",
            $this->getCacheTTL($user),
            function () use ($user, $moodleCourses) {
                $stats = $user->moodle_user_id && $moodleCourses
                    ? $this->calculateMoodleStats($user, $moodleCourses)
                    : $this->calculateLocalStats($user);
                
                // Log stats calculation for monitoring
                $this->logStatsCalculation($user, $stats);
                
                return $stats;
            },
            ['user_stats', 'dashboard', "user_{$user->id}"]
        );
    }

    /**
     * Calculate statistics from Moodle data
     */
    private function calculateMoodleStats(User $user, array $moodleCourses): array
    {
        $ctpCourse = $this->findCtpCourse($moodleCourses);
        $ctpProgress = $this->getCtpProgress($user, $ctpCourse);
        
        $baseStats = $this->getBaseStats($user);
        $moodleStats = $this->extractMoodleStats($moodleCourses, $ctpProgress);
        $examStats = $this->examDateManager->getExamStats($user);
        
        return array_merge($baseStats, $moodleStats, $examStats, [
            'course_name' => $ctpCourse['fullname'] ?? 'CTP Certification',
            'moodle_enrolled' => !empty($moodleCourses),
            'moodle_course_url' => $ctpCourse ? $this->generateMoodleCourseUrl($ctpCourse['id']) : null,
        ]);
    }

    /**
     * Calculate statistics from local data
     */
    private function calculateLocalStats(User $user): array
    {
        $enrollments = $user->courseEnrollments()->with('course')->get();
        
        $baseStats = $this->getBaseStats($user);
        $localStats = $this->extractLocalStats($user, $enrollments);
        $examStats = $this->examDateManager->getExamStats($user);
        
        return array_merge($baseStats, $localStats, $examStats, [
            'course_name' => 'CTP Certification',
            'moodle_enrolled' => false,
        ]);
    }

    /**
     * Get base statistics common to both Moodle and local calculations
     */
    private function getBaseStats(User $user): array
    {
        $engagementStats = $this->engagementTracker->getEngagementStats($user);
        
        return [
            'current_streak' => $user->current_streak ?? 0,
            'last_synced' => $user->last_synced_at?->diffForHumans() ?? 'Never synced',
            'activities_completed_today' => $this->progressCalculator->getActivitiesCompletedToday($user),
            'weekly_progress_trend' => $this->progressCalculator->getWeeklyProgressTrend($user),
            ...$engagementStats,
        ];
    }

    /**
     * Extract statistics from Moodle data
     */
    private function extractMoodleStats(array $moodleCourses, ?array $ctpProgress): array
    {
        $totalCourses = count($moodleCourses);
        $completedCourses = 0;
        $totalProgress = 0;
        $totalXP = 0;

        if ($ctpProgress) {
            $totalProgress = $ctpProgress['progress_percentage'] ?? 0;
            $totalXP = $ctpProgress['xp_earned'] ?? 0;
            $completedCourses = $totalProgress >= 100 ? 1 : 0;
        }

        return [
            'total_courses' => max($totalCourses, 1),
            'completed_courses' => $completedCourses,
            'total_xp' => $totalXP,
            'progress_percentage' => round($totalProgress, 1),
        ];
    }

    /**
     * Extract statistics from local enrollment data
     */
    private function extractLocalStats(User $user, Collection $enrollments): array
    {
        $totalCourses = $enrollments->count();
        $completedCourses = $enrollments->where('progress', 100)->count();
        $totalProgress = $enrollments->avg('progress') ?? 0;
        $totalXP = $user->getTotalXP() ?? 0;

        return [
            'total_courses' => max($totalCourses, 1),
            'completed_courses' => $completedCourses,
            'total_xp' => max($totalXP, 0),
            'progress_percentage' => round($totalProgress, 1),
        ];
    }

    /**
     * Find CTP course in Moodle courses array
     */
    private function findCtpCourse(array $moodleCourses): ?array
    {
        $ctpCourseId = config('beaconlms.ctp_course.id');
        
        foreach ($moodleCourses as $course) {
            if ($course['id'] == $ctpCourseId) {
                return $course;
            }
        }
        
        return null;
    }

    /**
     * Get CTP course progress from Moodle
     */
    private function getCtpProgress(User $user, ?array $ctpCourse): ?array
    {
        if (!$ctpCourse || !$user->moodle_user_id) {
            return null;
        }

        try {
            return $this->moodleRepository->getUserProgressData(
                $user->moodle_user_id, 
                $ctpCourse['id']
            );
        } catch (\Exception $e) {
            Log::warning('Failed to get CTP progress', [
                'user_id' => $user->id,
                'course_id' => $ctpCourse['id'],
                'error' => $e->getMessage()
            ]);
            
            return null;
        }
    }

    /**
     * Generate Moodle course URL
     */
    private function generateMoodleCourseUrl(int $courseId): string
    {
        $moodleUrl = config('beaconlms.moodle.url');
        return rtrim($moodleUrl, '/') . '/course/view.php?id=' . $courseId;
    }

    /**
     * Clear cached stats for a user with event broadcasting
     */
    public function clearStatsCache(User $user, array $dataTypes = ['stats']): void
    {
        $this->cacheManager->invalidateUserCache($user, $dataTypes);
        
        // Broadcast cache invalidation event for real-time updates
        event(new DataUpdatedEvent(
            'cache_invalidated',
            $user,
            ['cache_types' => $dataTypes],
            $dataTypes
        ));
    }
    
    /**
     * Update user stats and invalidate cache
     */
    public function updateUserStats(User $user, array $newData, string $updateType = 'general'): void
    {
        // Determine which cache types to invalidate based on update type
        $cacheTypes = $this->getCacheTypesForUpdate($updateType);
        
        // Clear relevant caches
        $this->clearStatsCache($user, $cacheTypes);
        
        // Warm cache with new data
        $this->cacheManager->warmCache($user);
        
        // Broadcast real-time update
        event(new DataUpdatedEvent(
            $updateType . '_updated',
            $user,
            $newData,
            $cacheTypes
        ));
    }
    
    /**
     * Get cache TTL based on user activity
     */
    private function getCacheTTL(User $user): int
    {
        // Shorter TTL for active users to ensure fresher data
        $lastActivity = $user->engagementLogs()->latest()->first();
        
        if ($lastActivity && $lastActivity->created_at->diffInMinutes(now()) < 10) {
            return 60; // 1 minute for very active users
        }
        
        if ($lastActivity && $lastActivity->created_at->diffInHours(now()) < 1) {
            return 180; // 3 minutes for recently active users
        }
        
        return self::CACHE_TTL; // 5 minutes for inactive users
    }
    
    /**
     * Determine cache types to invalidate based on update type
     */
    private function getCacheTypesForUpdate(string $updateType): array
    {
        $cacheTypeMap = [
            'progress' => ['stats', 'progress', 'dashboard'],
            'engagement' => ['stats', 'engagement', 'dashboard'],
            'exam' => ['stats', 'exam', 'dashboard'],
            'moodle_sync' => ['stats', 'progress', 'moodle', 'dashboard'],
            'general' => ['stats', 'dashboard'],
        ];
        
        return $cacheTypeMap[$updateType] ?? ['stats', 'dashboard'];
    }
    
    /**
     * Log stats calculation for monitoring and debugging
     */
    private function logStatsCalculation(User $user, array $stats): void
    {
        Log::info('Dashboard stats calculated', [
            'user_id' => $user->id,
            'has_moodle_data' => $stats['moodle_enrolled'] ?? false,
            'progress_percentage' => $stats['progress_percentage'] ?? 0,
            'calculation_time' => now(),
            'cache_strategy' => 'intelligent_caching'
        ]);
    }
}
