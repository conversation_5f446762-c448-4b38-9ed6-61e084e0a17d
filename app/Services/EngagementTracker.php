<?php

namespace App\Services;

use App\Models\User;
use App\Models\Course;
use App\Models\UserEngagementLog;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

/**
 * Service responsible for tracking and managing user engagement activities
 * 
 * Handles:
 * - Activity logging
 * - Study time tracking
 * - Engagement metrics calculation
 * - Activity type mapping
 */
class EngagementTracker
{
    private const ACTIVITY_STUDY_TIMES = [
        'dashboard_visit' => 0.05,    // 3 minutes
        'moodle_launch' => 0.1,       // 6 minutes
        'course_launch' => 0.25,      // 15 minutes
        'chapter_view' => 0.5,        // 30 minutes
        'quiz_attempt' => 1.0,        // 1 hour
        'assignment_submit' => 2.0,   // 2 hours
        'exam_date_update' => 0.05,   // 3 minutes
    ];

    /**
     * Log user engagement activity
     */
    public function logEngagement(User $user, string $activity, ?int $courseId = null): void
    {
        try {
            $course = $this->resolveCourse($courseId);
            $studyTime = $this->getStudyTimeForActivity($activity);

            if ($course && $studyTime > 0) {
                UserEngagementLog::logStudyTime($user, $course, $studyTime);
            }

            $this->logActivityForAnalytics($user, $activity, $courseId);

        } catch (\Exception $e) {
            Log::warning('Failed to log engagement', [
                'user_id' => $user->id,
                'activity' => $activity,
                'course_id' => $courseId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get engagement statistics for a user
     */
    public function getEngagementStats(User $user): array
    {
        $todayStudyHours = $user->engagementLogs()->today()->sum('study_hours');
        $weeklyStudyHours = $user->engagementLogs()->thisWeek()->sum('study_hours');
        $monthlyStudyHours = $user->engagementLogs()->thisMonth()->sum('study_hours');
        $totalStudyHours = $user->getStudyHours() ?? 0;

        return [
            'study_hours' => (int) $totalStudyHours,
            'today_study_hours' => (int) $todayStudyHours,
            'weekly_study_hours' => (int) $weeklyStudyHours,
            'monthly_study_hours' => (int) $monthlyStudyHours,
        ];
    }

    /**
     * Get study time for different activities
     */
    private function getStudyTimeForActivity(string $activity): float
    {
        return self::ACTIVITY_STUDY_TIMES[$activity] ?? 0;
    }

    /**
     * Resolve course for engagement logging
     */
    private function resolveCourse(?int $courseId): ?Course
    {
        if ($courseId) {
            return Course::where('moodle_course_id', $courseId)->first();
        }

        // Default to CTP course
        $ctpCourseId = config('beaconlms.ctp_course.id');
        return Course::where('moodle_course_id', $ctpCourseId)->first();
    }

    /**
     * Log activity for analytics purposes
     */
    private function logActivityForAnalytics(User $user, string $activity, ?int $courseId): void
    {
        Log::info('User engagement logged', [
            'user_id' => $user->id,
            'activity' => $activity,
            'course_id' => $courseId,
            'timestamp' => now(),
        ]);
    }

    /**
     * Get user's activity summary for a date range
     */
    public function getActivitySummary(User $user, Carbon $startDate, Carbon $endDate): array
    {
        $logs = $user->engagementLogs()
            ->whereBetween('date', [$startDate, $endDate])
            ->with('course')
            ->get();

        return [
            'total_study_hours' => $logs->sum('study_hours'),
            'active_days' => $logs->count(),
            'courses_studied' => $logs->pluck('course_id')->unique()->count(),
            'average_daily_hours' => $logs->avg('study_hours'),
        ];
    }

    /**
     * Check if user has been active today
     */
    public function isActiveToday(User $user): bool
    {
        $minimumMinutes = config('beaconlms.engagement.minimum_daily_activity_minutes', 30);
        $todayMinutes = $user->engagementLogs()->today()->sum('study_hours') * 60;

        return $todayMinutes >= $minimumMinutes;
    }

    /**
     * Get engagement trend for the last N days
     */
    public function getEngagementTrend(User $user, int $days = 30): array
    {
        $startDate = now()->subDays($days - 1)->startOfDay();
        $endDate = now()->endOfDay();

        return UserEngagementLog::getStudyHoursForPeriod($user, $startDate, $endDate);
    }
}
