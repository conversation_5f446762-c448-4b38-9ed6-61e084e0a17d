<?php

namespace App\Services;

use App\Models\User;
use App\Models\Course;
use App\Models\CourseEnrollment;
use App\Events\DataUpdatedEvent;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

/**
 * Service responsible for managing exam dates and related calculations
 * 
 * Handles:
 * - Exam date management
 * - Days remaining calculations
 * - Exam preparation timeline
 * - Default exam date assignments
 */
class ExamDateManager
{
    private const DEFAULT_PREPARATION_DAYS = 43;
    private const MINIMUM_PREPARATION_DAYS = 30;
    private const MAX_RESCHEDULES = 3;

    /**
     * Get exam-related statistics for a user
     */
    public function getExamStats(User $user): array
    {
        $examDate = $this->getUserExamDate($user);
        $daysLeft = $this->calculateDaysLeft($examDate);
        $hoursRequired = $this->calculateRequiredHours($user);
        $hoursCompleted = $this->calculateCompletedHours($user);

        return [
            'days_left_exam' => $daysLeft,
            'hours_completed' => $hoursCompleted,
            'total_hours_required' => $hoursRequired,
            'exam_date' => $examDate,
            'preparation_progress' => $this->calculatePreparationProgress($hoursCompleted, $hoursRequired),
        ];
    }

    /**
     * Get user's exam date or return default
     */
    public function getUserExamDate(User $user): Carbon
    {
        $enrollment = $this->getCtpEnrollment($user);
        
        if ($enrollment && $enrollment->exam_date) {
            return $enrollment->exam_date;
        }

        return now()->addDays(self::DEFAULT_PREPARATION_DAYS);
    }

    /**
     * Update user's exam date
     */
    public function updateExamDate(User $user, Carbon $newExamDate): CourseEnrollment
    {
        $this->validateExamDate($newExamDate);
        
        $enrollment = $this->getOrCreateCtpEnrollment($user);
        $oldExamDate = $enrollment->exam_date;
        
        // Log the change for audit purposes
        Log::info('Exam date updated', [
            'user_id' => $user->id,
            'old_date' => $oldExamDate,
            'new_date' => $newExamDate,
        ]);

        $enrollment->update(['exam_date' => $newExamDate]);
        
        // Fire event to trigger instant cache invalidation
        event(new DataUpdatedEvent(
            eventType: 'exam_date_changed',
            user: $user,
            data: [
                'course_id' => $enrollment->course_id,
                'old_exam_date' => $oldExamDate?->toDateString(),
                'new_exam_date' => $newExamDate->toDateString(),
                'enrollment_id' => $enrollment->id
            ],
            affectedCacheTypes: ['exam', 'stats', 'dashboard']
        ));
        
        return $enrollment;
    }

    /**
     * Calculate days remaining until exam
     */
    private function calculateDaysLeft(Carbon $examDate): int
    {
        return (int) now()->diffInDays($examDate, false);
    }

    /**
     * Calculate required study hours based on course total hours
     */
    private function calculateRequiredHours(User $user = null): int
    {
        // If user is provided, get course-specific hours
        if ($user) {
            $enrollment = $this->getCtpEnrollment($user);
            if ($enrollment && $enrollment->course && $enrollment->course->total_hours) {
                return (int) $enrollment->course->total_hours;
            }
        }
        
        // Fallback to CTP course total hours from database
        $ctpCourse = $this->getOrCreateCtpCourse();
        if ($ctpCourse->total_hours) {
            return (int) $ctpCourse->total_hours;
        }
        
        // Final fallback to config-based calculation
        $preparationDays = config('beaconlms.ctp_course.exam_preparation_days', self::DEFAULT_PREPARATION_DAYS);
        $hoursPerDay = config('beaconlms.ctp_course.hours_per_day', 12);
        
        return (int) ($preparationDays * $hoursPerDay);
    }

    /**
     * Calculate completed study hours for user based on course progress
     */
    private function calculateCompletedHours(User $user): int
    {
        $enrollment = $this->getCtpEnrollment($user);
        
        if (!$enrollment || !$enrollment->course) {
            return (int) ($user->getStudyHours() ?? 0);
        }
        
        // Calculate completed hours based on course total hours and progress percentage
        $totalCourseHours = $enrollment->course->total_hours ?? 0;
        $progressPercentage = $enrollment->progress ?? 0;
        
        if ($totalCourseHours > 0) {
            return (int) (($progressPercentage / 100) * $totalCourseHours);
        }
        
        // Fallback to user's study hours if course hours not available
        return (int) ($user->getStudyHours() ?? 0);
    }

    /**
     * Calculate preparation progress percentage
     */
    private function calculatePreparationProgress(int $completed, int $required): float
    {
        if ($required <= 0) {
            return 0.0;
        }

        return round(($completed / $required) * 100, 1);
    }

    /**
     * Get CTP course enrollment for user
     */
    private function getCtpEnrollment(User $user): ?CourseEnrollment
    {
        return $user->courseEnrollments()
            ->whereHas('course', function ($query) {
                $query->where('moodle_course_id', config('beaconlms.ctp_course.id'));
            })
            ->first();
    }

    /**
     * Get or create CTP course enrollment
     */
    private function getOrCreateCtpEnrollment(User $user): CourseEnrollment
    {
        $enrollment = $this->getCtpEnrollment($user);
        
        if ($enrollment) {
            return $enrollment;
        }

        // Create CTP course if it doesn't exist
        $ctpCourse = $this->getOrCreateCtpCourse();
        
        // Create enrollment
        return CourseEnrollment::create([
            'user_id' => $user->id,
            'course_id' => $ctpCourse->id,
            'progress' => 0,
            'enrolled_at' => now(),
            'exam_date' => now()->addDays(self::DEFAULT_PREPARATION_DAYS),
        ]);
    }

    /**
     * Get or create CTP course
     */
    private function getOrCreateCtpCourse(): Course
    {
        $ctpCourse = Course::where('moodle_course_id', config('beaconlms.ctp_course.id'))->first();
        
        if ($ctpCourse) {
            return $ctpCourse;
        }

        return Course::create([
            'title' => config('beaconlms.ctp_course.title', 'CTP Certification'),
            'description' => config('beaconlms.ctp_course.description', 'CTP Certification Course'),
            'moodle_course_id' => config('beaconlms.ctp_course.id'),
            'is_active' => true,
        ]);
    }

    /**
     * Validate exam date
     */
    private function validateExamDate(Carbon $examDate): void
    {
        if ($examDate->isPast()) {
            throw new \InvalidArgumentException('Exam date cannot be in the past');
        }

        $minimumDate = now()->addDays(self::MINIMUM_PREPARATION_DAYS);
        if ($examDate->lt($minimumDate)) {
            throw new \InvalidArgumentException(
                "Exam date must be at least " . self::MINIMUM_PREPARATION_DAYS . " days from now"
            );
        }
    }

    /**
     * Check if user can reschedule exam
     */
    public function canRescheduleExam(User $user): bool
    {
        $rescheduleCount = $user->exam_reschedule_count ?? 0;
        return $rescheduleCount < self::MAX_RESCHEDULES;
    }

    /**
     * Get exam preparation timeline
     */
    public function getPreparationTimeline(Carbon $examDate): array
    {
        $now = now();
        $totalDays = $now->diffInDays($examDate);
        
        return [
            'total_days' => $totalDays,
            'weeks_remaining' => (int) ceil($totalDays / 7),
            'months_remaining' => (int) ceil($totalDays / 30),
            'milestone_dates' => $this->calculateMilestoneDates($examDate),
        ];
    }

    /**
     * Calculate milestone dates for exam preparation
     */
    private function calculateMilestoneDates(Carbon $examDate): array
    {
        return [
            '75_percent' => $examDate->copy()->subDays((int) ($examDate->diffInDays(now()) * 0.25)),
            '50_percent' => $examDate->copy()->subDays((int) ($examDate->diffInDays(now()) * 0.5)),
            '25_percent' => $examDate->copy()->subDays((int) ($examDate->diffInDays(now()) * 0.75)),
            'final_week' => $examDate->copy()->subWeek(),
        ];
    }
}
