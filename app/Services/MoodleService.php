<?php

namespace App\Services;

use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class MoodleService
{
    private Client $client;
    private string $baseUrl;
    private string $token;
    private string $service;
    private int $timeout;

    public function __construct(
        string $baseUrl = null,
        string $token = null,
        string $service = null,
        int $timeout = null
    ) {
        $this->baseUrl = $baseUrl ?? config('beaconlms.moodle.url');
        $this->token = $token ?? config('beaconlms.moodle.token');
        $this->service = $service ?? config('beaconlms.moodle.service');
        $this->timeout = $timeout ?? config('beaconlms.moodle.timeout');

        $this->client = new Client([
            'timeout' => $this->timeout,
            'verify' => false, // For local development
        ]);
    }

    /**
     * Make a request to Moodle Web Service API
     * @throws Exception|\GuzzleHttp\Exception\GuzzleException
     */
    private function makeRequest(string $function, array $params = []): array
    {
        try {
            $requestParams = [
                'wstoken' => $this->token,
                'wsfunction' => $function,
                'moodlewsrestformat' => 'json',
            ];

            // Merge function-specific parameters
            $requestParams = array_merge($requestParams, $params);

            Log::info('Moodle API Request', [
                'function' => $function,
                'params' => $params,
                'url' => $this->baseUrl
            ]);

            $response = $this->client->post($this->baseUrl, [
                'form_params' => $requestParams,
            ]);

            $data = json_decode($response->getBody()->getContents(), true);

            // Check for Moodle API errors
            if (isset($data['exception']) || isset($data['errorcode'])) {
                throw new Exception('Moodle API Error: ' . ($data['message'] ?? $data['errorcode'] ?? 'Unknown error'));
            }

            Log::info('Moodle API Response', [
                'function' => $function,
                'response_size' => strlen(json_encode($data))
            ]);

            return $data;

        } catch (RequestException $e) {
            Log::error('Moodle API Request Failed', [
                'function' => $function,
                'error' => $e->getMessage(),
                'url' => $this->baseUrl
            ]);
            throw new Exception('Failed to connect to Moodle: ' . $e->getMessage());
        }
    }

    /**
     * Test connection to Moodle
     */
    public function testConnection(): array
    {
        return $this->makeRequest('core_webservice_get_site_info');
    }

    // ========================================
    // USER MANAGEMENT METHODS
    // ========================================

    /**
     * Create users in Moodle
     */
    public function createUsers(array $users): array
    {
        $formattedUsers = [];
        foreach ($users as $index => $user) {
            $formattedUsers["users[{$index}][username]"] = $user['username'];
            $formattedUsers["users[{$index}][password]"] = $user['password'];
            $formattedUsers["users[{$index}][firstname]"] = $user['firstname'];
            $formattedUsers["users[{$index}][lastname]"] = $user['lastname'];
            $formattedUsers["users[{$index}][email]"] = $user['email'];
        }

        return $this->makeRequest('core_user_create_users', $formattedUsers);
    }

    /**
     * Get users by field
     */
    public function getUsersByField(string $field, array $values): array
    {
        $params = [];

        foreach ($values as $index => $value) {
            $params["criteria[{$index}][key]"] = $field;
            $params["criteria[{$index}][value]"] = $value;
        }

        return $this->makeRequest('core_user_get_users', $params);
    }

    /**
     * Update users in Moodle
     */
    public function updateUsers(array $users): array
    {
        $formattedUsers = [];
        foreach ($users as $index => $user) {
            $formattedUsers["users[{$index}][id]"] = $user['id'];
            if (isset($user['username'])) {
                $formattedUsers["users[{$index}][username]"] = $user['username'];
            }
            if (isset($user['firstname'])) {
                $formattedUsers["users[{$index}][firstname]"] = $user['firstname'];
            }
            if (isset($user['lastname'])) {
                $formattedUsers["users[{$index}][lastname]"] = $user['lastname'];
            }
            if (isset($user['email'])) {
                $formattedUsers["users[{$index}][email]"] = $user['email'];
            }
        }

        return $this->makeRequest('core_user_update_users', $formattedUsers);
    }

    /**
     * Get a single user by email address
     *
     * @param string $email
     * @return array|null User data or null if not found
     */
    public function getUserByEmail(string $email): ?array
    {
        $users = $this->getUsersByField('email', [$email]);

        if (empty($users['users'])) {
            return null;
        }

        return $users['users'][0];
    }

    /**
     * Create a single user in Moodle
     *
     * @param array $userData
     * @return array|null Created user data or null on failure
     */
    public function createUser(array $userData): ?array
    {
        $users = $this->createUsers([$userData]);

        if (empty($users['users'])) {
            return null;
        }

        return $users['users'][0];
    }

    // ========================================
    // COURSE MANAGEMENT METHODS
    // ========================================

    /**
     * Get courses
     */
    public function getCourses(array $options = []): array
    {
        $cacheKey = 'moodle_courses_' . md5(serialize($options));

        return Cache::remember($cacheKey, config('beaconlms.engagement.progress_cache_ttl'), function () use ($options) {
            return $this->makeRequest('core_course_get_courses', $options);
        });
    }

    /**
     * Get course contents
     */
    public function getCourseContents(int $courseId, array $options = []): array
    {
        $params = array_merge(['courseid' => $courseId], $options);

        $cacheKey = 'moodle_course_contents_' . $courseId;

        return Cache::remember($cacheKey, config('beaconlms.engagement.progress_cache_ttl'), function () use ($params) {
            return $this->makeRequest('core_course_get_contents', $params);
        });
    }

    /**
     * Get user's enrolled courses
     */
    public function getUserCourses(int $userId): array
    {
        $params = ['userid' => $userId];

        $cacheKey = 'moodle_user_courses_' . $userId;

        return Cache::remember($cacheKey, config('beaconlms.engagement.progress_cache_ttl'), function () use ($params) {
            return $this->makeRequest('core_enrol_get_users_courses', $params);
        });
    }

    // ========================================
    // ENROLLMENT METHODS
    // ========================================

    /**
     * Enroll users in courses
     */
    public function enrollUsers(array $enrollments): array
    {
        $formattedEnrollments = [];
        foreach ($enrollments as $index => $enrollment) {
            $formattedEnrollments["enrolments[{$index}][roleid]"] = $enrollment['roleid'] ?? 5; // Student role
            $formattedEnrollments["enrolments[{$index}][userid]"] = $enrollment['userid'];
            $formattedEnrollments["enrolments[{$index}][courseid]"] = $enrollment['courseid'];
        }

        return $this->makeRequest('enrol_manual_enrol_users', $formattedEnrollments);
    }

    /**
     * Unenroll users from courses
     */
    public function unenrollUsers(array $enrollments): array
    {
        $formattedEnrollments = [];
        foreach ($enrollments as $index => $enrollment) {
            $formattedEnrollments["enrolments[{$index}][userid]"] = $enrollment['userid'];
            $formattedEnrollments["enrolments[{$index}][courseid]"] = $enrollment['courseid'];
        }

        return $this->makeRequest('enrol_manual_unenrol_users', $formattedEnrollments);
    }

    /**
     * Get enrolled users in a course
     */
    public function getEnrolledUsers(int $courseId, array $options = []): array
    {
        $params = array_merge(['courseid' => $courseId], $options);

        return $this->makeRequest('core_enrol_get_enrolled_users', $params);
    }

    // ========================================
    // PROGRESS TRACKING METHODS
    // ========================================

    /**
     * Get course completion status for a user
     */
    public function getCourseCompletion(int $userId, int $courseId): array
    {
        $params = [
            'userid' => $userId,
            'courseid' => $courseId,
        ];

        $cacheKey = 'moodle_completion_' . $userId . '_' . $courseId;

        return Cache::remember($cacheKey, config('beaconlms.engagement.progress_cache_ttl'), function () use ($params) {
            return $this->makeRequest('core_completion_get_course_completion_status', $params);
        });
    }

    /**
     * Get activities completion status
     */
    public function getActivitiesCompletion(int $userId, int $courseId): array
    {
        $params = [
            'userid' => $userId,
            'courseid' => $courseId,
        ];

        $cacheKey = 'moodle_activities_completion_' . $userId . '_' . $courseId;

        return Cache::remember($cacheKey, config('beaconlms.engagement.progress_cache_ttl'), function () use ($params) {
            return $this->makeRequest('core_completion_get_activities_completion_status', $params);
        });
    }

    /**
     * Get user grades for a course
     */
    public function getUserGrades(int $userId, int $courseId): array
    {
        $params = [
            'userid' => $userId,
            'courseid' => $courseId,
        ];

        $cacheKey = 'moodle_grades_' . $userId . '_' . $courseId;

        return Cache::remember($cacheKey, config('beaconlms.engagement.progress_cache_ttl'), function () use ($params) {
            return $this->makeRequest('gradereport_user_get_grade_items', $params);
        });
    }

    // ========================================
    // ACTIVITY METHODS
    // ========================================

    /**
     * Get quizzes by courses
     */
    public function getQuizzesByCourses(array $courseIds = []): array
    {
        $params = [];
        if (!empty($courseIds)) {
            foreach ($courseIds as $index => $courseId) {
                $params["courseids[{$index}]"] = $courseId;
            }
        }

        $cacheKey = 'moodle_quizzes_' . md5(serialize($courseIds));

        return Cache::remember($cacheKey, config('beaconlms.engagement.progress_cache_ttl'), function () use ($params) {
            return $this->makeRequest('mod_quiz_get_quizzes_by_courses', $params);
        });
    }

    /**
     * Get user quiz attempts
     */
    public function getQuizAttempts(int $quizId, int $userId = null): array
    {
        $params = ['quiz' => $quizId];
        if ($userId) {
            $params['userid'] = $userId;
        }

        return $this->makeRequest('mod_quiz_get_user_attempts', $params);
    }

    /**
     * Get assignments by courses
     */
    public function getAssignmentsByCourses(array $courseIds = []): array
    {
        $params = [];
        if (!empty($courseIds)) {
            foreach ($courseIds as $index => $courseId) {
                $params["courseids[{$index}]"] = $courseId;
            }
        }

        $cacheKey = 'moodle_assignments_' . md5(serialize($courseIds));

        return Cache::remember($cacheKey, config('beaconlms.engagement.progress_cache_ttl'), function () use ($params) {
            return $this->makeRequest('mod_assign_get_assignments', $params);
        });
    }

    /**
     * Get assignment submission status
     */
    public function getAssignmentSubmissionStatus(int $assignId, int $userId = null): array
    {
        $params = ['assignid' => $assignId];
        if ($userId) {
            $params['userid'] = $userId;
        }

        return $this->makeRequest('mod_assign_get_submission_status', $params);
    }

    // ========================================
    // UTILITY METHODS
    // ========================================

    /**
     * Clear cache for a specific user
     */
    public function clearUserCache(int $userId): void
    {
        $patterns = [
            'moodle_user_courses_' . $userId,
            'moodle_completion_' . $userId . '_*',
            'moodle_activities_completion_' . $userId . '_*',
            'moodle_grades_' . $userId . '_*',
        ];

        foreach ($patterns as $pattern) {
            Cache::forget($pattern);
        }
    }

    /**
     * Clear all Moodle cache
     */
    public function clearAllCache(): void
    {
        $keys = [
            'moodle_courses_*',
            'moodle_course_contents_*',
            'moodle_user_courses_*',
            'moodle_completion_*',
            'moodle_activities_completion_*',
            'moodle_grades_*',
            'moodle_quizzes_*',
            'moodle_assignments_*',
        ];

        foreach ($keys as $key) {
            Cache::forget($key);
        }
    }
}
