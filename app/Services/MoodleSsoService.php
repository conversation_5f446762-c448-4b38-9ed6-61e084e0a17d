<?php

namespace App\Services;

use App\Models\User;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Carbon\Carbon;

class MoodleSsoService
{
    protected $moodleUrl;
    protected $moodleToken;
    protected $moodleService;
    protected $tokenExpiryMinutes = 5; // SSO token valid for 5 minutes

    public function __construct(MoodleService $moodleService = null)
    {
        $this->moodleUrl = config('beaconlms.moodle.url');
        $this->moodleToken = config('beaconlms.moodle.token');

        $this->moodleService = $moodleService ?? new MoodleService();
    }

    /**
     * Generate a secure one-time SSO login URL for Moodle.
     * Uses HMAC-SHA256 for cryptographic security.
     *
     * @param User $user
     * @return string|null
     */
    public function generateSsoUrl(User $user): ?string
    {
        if (!$this->isConfigured()) {
            Log::warning('Moodle SSO is not configured. Please check your .env file.');
            return null;
        }

        try {
            // Step 1: Get or create Moodle user with caching
            $moodleUser = $this->getCachedMoodleUser($user);
            if (!$moodleUser) {
                throw new \Exception('Could not get or create Moodle user.');
            }

            // Step 2: Generate secure SSO URL with HMAC
            $timestamp = time();
            $nonce = Str::random(16); // Add randomness to prevent replay attacks
            $token = $this->generateSecureToken($user, $timestamp, $nonce);

            // Log SSO attempt for security audit
            Log::info('SSO URL generated', [
                'user_id' => $user->id,
                'moodle_user_id' => $moodleUser['id'],
                'timestamp' => $timestamp
            ]);

            $queryParams = http_build_query([
                'username' => $moodleUser['username'],
                'timestamp' => $timestamp,
                'nonce' => $nonce,
                'token' => $token,
                'beacon_user_id' => $user->id,
            ]);

            // Correctly build the SSO URL by ensuring we use the Moodle base path,
            // not the full API endpoint path.
            $parsedUrl = parse_url($this->moodleUrl);
            $baseUrl = $parsedUrl['scheme'] . '://' . $parsedUrl['host'];
            if (isset($parsedUrl['port'])) {
                $baseUrl .= ':' . $parsedUrl['port'];
            }

            $ssoUrl = "{$baseUrl}/auth/beaconlms/sso.php?{$queryParams}";

            // Cache the SSO URL for 5 minutes to prevent regeneration
            Cache::put("sso_url_{$user->id}", $ssoUrl, now()->addMinutes($this->tokenExpiryMinutes));

            return $ssoUrl;

        } catch (\Exception $e) {
            Log::error('Moodle SSO URL generation failed', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * Generate cryptographically secure token using SHA256
     * This must match the algorithm used in the Moodle plugin
     *
     * @param User $user
     * @param int $timestamp
     * @param string $nonce
     * @return string
     */
    private function generateSecureToken(User $user, int $timestamp, string $nonce): string
    {
        // Use the shared secret configured for SSO
        $secret = config('beaconlms.sso.shared_secret');

        if (empty($secret)) {
            throw new \Exception('SSO shared secret not configured. Please set BEACON_SSO_SECRET in your .env file.');
        }

        // This data format must match exactly what the Moodle plugin expects
        $data = $user->email . '|' . $timestamp . '|' . $nonce . '|' . $user->id . '|' . $secret;

        return hash('sha256', $data);
    }

    /**
     * Validate SSO token (for Moodle-side validation)
     *
     * @param User $user
     * @param int $timestamp
     * @param string $nonce
     * @param string $token
     * @return bool
     */
    public function validateSsoToken(User $user, int $timestamp, string $nonce, string $token): bool
    {
        // Check token expiry
        if (!$this->isTokenValid($timestamp)) {
            Log::warning('Expired SSO token attempt', ['user_id' => $user->id]);
            return false;
        }

        // Regenerate token and compare
        $expectedToken = $this->generateSecureToken($user, $timestamp, $nonce);

        if (!hash_equals($expectedToken, $token)) {
            Log::warning('Invalid SSO token attempt', ['user_id' => $user->id]);
            return false;
        }

        return true;
    }

    /**
     * Check if token is within valid time window
     *
     * @param int $timestamp
     * @return bool
     */
    private function isTokenValid(int $timestamp): bool
    {
        $maxAge = $this->tokenExpiryMinutes * 60; // Convert to seconds
        return (time() - $timestamp) <= $maxAge;
    }

    /**
     * Get Moodle user with caching for performance
     *
     * @param User $user
     * @return array|null
     */
    private function getCachedMoodleUser(User $user): ?array
    {
        $cacheKey = "moodle_user_{$user->email}";

        return Cache::remember($cacheKey, 3600, function() use ($user) {
            return $this->getOrCreateMoodleUser($user);
        });
    }

    /**
     * Get a Moodle user by email, or create them if they do not exist.
     * Enhanced with better error handling and user data mapping.
     *
     * @param User $user
     * @return array|null
     */
    private function getOrCreateMoodleUser(User $user): ?array
    {
        try {
            // Try to get existing user using the existing getUsersByField method
            $moodleUsers = $this->moodleService->getUsersByField('email', [$user->email]);

            if (!empty($moodleUsers['users']) && count($moodleUsers['users']) > 0) {
                $moodleUser = $moodleUsers['users'][0];

                // Update local record if needed
                if (!$user->moodle_user_id) {
                    $user->moodle_user_id = $moodleUser['id'];
                    $user->save();
                }
                return $moodleUser;
            }

            // User doesn't exist, create them with proper name parsing
            $nameParts = explode(' ', $user->name, 2);
            $firstName = $nameParts[0] ?? $user->name;
            $lastName = $nameParts[1] ?? '';

            $userData = [
                'username' => $user->email,
                'password' => 'BeaconSSO_' . Str::random(16), // Secure random password
                'firstname' => $firstName,
                'lastname' => $lastName,
                'email' => $user->email,
                'auth' => 'manual', // Set auth method
                'idnumber' => 'beacon_' . $user->id, // Link to BeaconLMS
                'lang' => 'en', // Set default language
                'timezone' => 'America/New_York', // Set default timezone
                'mailformat' => 1, // Set default mail format
                'autosubscribe' => 1, // Set default autosubscribe
            ];

            // Use existing createUsers method
            $newUsers = $this->moodleService->createUsers([$userData]);

            Log::info('Moodle user creation response', [
                'user_email' => $user->email,
                'response' => $newUsers
            ]);

            if (!empty($newUsers['users']) && count($newUsers['users']) > 0) {
                $newUser = $newUsers['users'][0];

                // Update our user record with the new Moodle ID
                $user->moodle_user_id = $newUser['id'];
                $user->save();

                // Clear cache to ensure fresh data
                Cache::forget("moodle_user_{$user->email}");

                Log::info('New Moodle user created via SSO', [
                    'beacon_user_id' => $user->id,
                    'moodle_user_id' => $newUser['id'],
                    'email' => $user->email
                ]);

                return $newUser;
            }

            // If user creation failed, try to find the user again
            // Sometimes Moodle creates the user but returns an error
            Log::warning('User creation response was empty, trying to find user again', [
                'user_email' => $user->email,
                'response' => $newUsers
            ]);

            // Wait a moment and try to find the user
            sleep(1);
            $retryUsers = $this->moodleService->getUsersByField('email', [$user->email]);

            if (!empty($retryUsers['users']) && count($retryUsers['users']) > 0) {
                $foundUser = $retryUsers['users'][0];

                // Update our user record with the found Moodle ID
                $user->moodle_user_id = $foundUser['id'];
                $user->save();

                Log::info('Found Moodle user after failed creation response', [
                    'beacon_user_id' => $user->id,
                    'moodle_user_id' => $foundUser['id'],
                    'email' => $user->email
                ]);

                return $foundUser;
            }

            throw new \Exception('Failed to create Moodle user and user not found on retry');

        } catch (\Exception $e) {
            Log::error('Error in getOrCreateMoodleUser', [
                'user_id' => $user->id,
                'email' => $user->email,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Get cached SSO URL if available
     *
     * @param User $user
     * @return string|null
     */
    public function getCachedSsoUrl(User $user): ?string
    {
        return Cache::get("sso_url_{$user->id}");
    }

    /**
     * Clear SSO cache for user (useful after logout)
     *
     * @param User $user
     * @return void
     */
    public function clearSsoCache(User $user): void
    {
        Cache::forget("sso_url_{$user->id}");
        Cache::forget("moodle_user_{$user->email}");
    }

    /**
     * Check if Moodle integration is properly configured.
     *
     * @return bool
     */
    public function isConfigured(): bool
    {
        return !empty($this->moodleUrl) &&
               !empty($this->moodleToken) &&
               filter_var($this->moodleUrl, FILTER_VALIDATE_URL);
    }

    /**
     * Get SSO configuration status for debugging
     *
     * @return array
     */
    public function getConfigStatus(): array
    {
        return [
            'moodle_url_configured' => !empty($this->moodleUrl),
            'moodle_url_valid' => filter_var($this->moodleUrl, FILTER_VALIDATE_URL),
            'moodle_token_configured' => !empty($this->moodleToken),
            'service_ready' => $this->isConfigured()
        ];
    }
}
