<?php

namespace App\Services;

use App\Models\User;
use App\Models\UserSession;
use App\Events\DataUpdatedEvent;
use App\Services\CacheManager;
use App\Services\DashboardStatsService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

/**
 * Real-Time Dashboard Service (Database Cache Compatible)
 * 
 * Handles real-time dashboard updates with intelligent caching
 * using database-based session tracking instead of Redis
 */
class RealTimeDashboardService
{
    public function __construct(
        private readonly CacheManager $cacheManager,
        private readonly DashboardStatsService $dashboardStatsService
    ) {}

    /**
     * Get real-time dashboard data with intelligent caching
     */
    public function getRealTimeDashboardData(User $user): array
    {
        // Check if user has real-time updates enabled
        $isRealTimeEnabled = $this->isRealTimeEnabled($user);
        
        if ($isRealTimeEnabled) {
            return $this->getActiveUserDashboard($user);
        }
        
        return $this->getStandardDashboard($user);
    }

    /**
     * Get dashboard for active users with shorter cache TTL
     */
    private function getActiveUserDashboard(User $user): array
    {
        return $this->cacheManager->remember(
            "realtime_dashboard_{$user->id}",
            60, // 1 minute cache for active users
            function () use ($user) {
                return $this->calculateRealTimeStats($user);
            },
            ['realtime', 'dashboard', "user_{$user->id}"]
        );
    }

    /**
     * Get standard dashboard with normal caching
     */
    private function getStandardDashboard(User $user): array
    {
        return $this->dashboardStatsService->getDashboardStats($user);
    }

    /**
     * Calculate real-time statistics with live data
     */
    private function calculateRealTimeStats(User $user): array
    {
        $baseStats = $this->dashboardStatsService->getDashboardStats($user);
        
        // Add real-time metrics
        $realTimeMetrics = [
            'live_study_time' => $this->getLiveStudyTime($user),
            'current_activity' => $this->getCurrentActivity($user),
            'real_time_progress' => $this->getRealTimeProgress($user),
            'live_streak_status' => $this->getLiveStreakStatus($user),
            'last_updated' => now()->toISOString(),
        ];
        
        return array_merge($baseStats, $realTimeMetrics);
    }

    /**
     * Handle real-time data updates
     */
    public function handleRealTimeUpdate(string $eventType, User $user, array $data): void
    {
        // Invalidate real-time cache immediately
        $this->invalidateRealTimeCache($user);
        
        // Broadcast update to connected clients
        $this->broadcastUpdate($eventType, $user, $data);
        
        // Log real-time update
        Log::info('Real-time dashboard update', [
            'event_type' => $eventType,
            'user_id' => $user->id,
            'data' => $data,
            'timestamp' => now()
        ]);
    }

    /**
     * Check if real-time updates are enabled for user (Database-based)
     */
    private function isRealTimeEnabled(User $user): bool
    {
        // Check if user has an active session within 5 minutes
        $activeSession = UserSession::getActiveSession($user);
        
        return $activeSession && $activeSession->isStillActive();
    }

    /**
     * Get live study time for current session (Database-based)
     */
    private function getLiveStudyTime(User $user): float
    {
        $activeSession = UserSession::getActiveSession($user);
        
        if (!$activeSession) {
            return 0.0;
        }
        
        return $activeSession->getDurationInHours();
    }

    /**
     * Get current user activity (Database-based)
     */
    private function getCurrentActivity(User $user): ?string
    {
        $activeSession = UserSession::getActiveSession($user);
        
        return $activeSession?->activity_type;
    }

    /**
     * Get real-time progress updates (Database-based)
     */
    private function getRealTimeProgress(User $user): array
    {
        // Get progress from database cache or calculate
        $progressData = $this->cacheManager->remember(
            "realtime_progress_{$user->id}",
            60, // 1 minute cache
            function () use ($user) {
                return $this->calculateRealTimeProgressFromDB($user);
            },
            ['realtime_progress', "user_{$user->id}"]
        );
        
        return $progressData;
    }

    /**
     * Get live streak status (Database-based)
     */
    private function getLiveStreakStatus(User $user): array
    {
        $currentStreak = $user->current_streak ?? 0;
        $todayActive = $this->isTodayActive($user);
        
        return [
            'current_streak' => $currentStreak,
            'today_active' => $todayActive,
            'streak_at_risk' => $this->isStreakAtRisk($user),
            'next_milestone' => $this->getNextStreakMilestone($currentStreak),
        ];
    }

    /**
     * Check if user's streak is at risk
     */
    private function isStreakAtRisk(User $user): bool
    {
        $lastActivity = $user->engagementLogs()->today()->latest()->first();
        
        if (!$lastActivity) {
            return true; // No activity today
        }
        
        $minimumDailyMinutes = config('beaconlms.engagement.minimum_daily_activity_minutes', 30);
        $todayMinutes = $user->engagementLogs()->today()->sum('study_hours') * 60;
        
        return $todayMinutes < $minimumDailyMinutes;
    }

    /**
     * Get next streak milestone
     */
    private function getNextStreakMilestone(int $currentStreak): int
    {
        $milestones = [7, 14, 30, 60, 100, 365];
        
        foreach ($milestones as $milestone) {
            if ($currentStreak < $milestone) {
                return $milestone;
            }
        }
        
        return $currentStreak + 100; // Next hundred milestone
    }

    /**
     * Calculate real-time progress from database
     */
    private function calculateRealTimeProgressFromDB(User $user): array
    {
        $today = now()->startOfDay();
        
        // Get today's completed items
        $itemsCompletedToday = $user->courseEnrollments()
            ->with('course.chapters.items')
            ->get()
            ->flatMap(function ($enrollment) {
                return $enrollment->course->chapters->flatMap->items;
            })
            ->where('is_completed', true)
            ->where('updated_at', '>=', $today)
            ->count();
        
        // Get today's study time
        $timeSpentToday = $user->engagementLogs()
            ->whereDate('date', $today)
            ->sum('study_hours');
        
        // Get current chapter from active session
        $activeSession = UserSession::getActiveSession($user);
        $currentChapter = $activeSession?->data['current_chapter'] ?? null;
        
        // Calculate completion rate
        $totalItems = $user->courseEnrollments()
            ->with('course.chapters.items')
            ->get()
            ->flatMap(function ($enrollment) {
                return $enrollment->course->chapters->flatMap->items;
            })
            ->count();
        
        $completionRate = $totalItems > 0 ? ($itemsCompletedToday / $totalItems) * 100 : 0;
        
        return [
            'items_completed_today' => $itemsCompletedToday,
            'time_spent_today' => $timeSpentToday,
            'current_chapter' => $currentChapter,
            'completion_rate' => round($completionRate, 2),
        ];
    }
    
    /**
     * Check if user is active today
     */
    private function isTodayActive(User $user): bool
    {
        return UserSession::where('user_id', $user->id)
            ->whereDate('started_at', now()->toDateString())
            ->exists();
    }

    /**
     * Invalidate real-time cache
     */
    private function invalidateRealTimeCache(User $user): void
    {
        $this->cacheManager->invalidateUserCache($user, ['realtime', 'dashboard']);
    }

    /**
     * Broadcast update to connected clients
     */
    private function broadcastUpdate(string $eventType, User $user, array $data): void
    {
        event(new DataUpdatedEvent(
            $eventType,
            $user,
            $data,
            ['realtime', 'dashboard']
        ));
    }

    /**
     * Start real-time tracking for user (Database-based)
     */
    public function startRealTimeTracking(User $user, string $activity = 'dashboard'): void
    {
        // Start or update user session
        $activeSession = UserSession::getActiveSession($user);
        
        if ($activeSession && $activeSession->isStillActive()) {
            // Update existing session
            $activeSession->updateActivity($activity);
        } else {
            // Start new session
            UserSession::startSession($user, $activity);
        }
        
        Log::info('Real-time tracking started', [
            'user_id' => $user->id,
            'activity' => $activity,
            'timestamp' => now()
        ]);
    }

    /**
     * Stop real-time tracking for user (Database-based)
     */
    public function stopRealTimeTracking(User $user): void
    {
        // End active session
        $activeSession = UserSession::getActiveSession($user);
        if ($activeSession) {
            $activeSession->endSession();
        }
        
        // Invalidate real-time cache
        $this->invalidateRealTimeCache($user);
        
        Log::info('Real-time tracking stopped', [
            'user_id' => $user->id,
            'timestamp' => now()
        ]);
    }

    /**
     * Update real-time progress (Database-based)
     */
    public function updateRealTimeProgress(User $user, array $progressData): void
    {
        // Update session data
        $activeSession = UserSession::getActiveSession($user);
        if ($activeSession) {
            $activeSession->updateActivity(null, ['progress' => $progressData]);
        }
        
        // Invalidate progress cache
        $this->cacheManager->invalidateUserCache($user, ['realtime_progress', 'progress']);
        
        // Broadcast progress update
        $this->handleRealTimeUpdate('progress_updated', $user, $progressData);
    }
}
