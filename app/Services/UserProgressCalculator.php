<?php

namespace App\Services;

use App\Models\User;
use App\Models\UserEngagementLog;
use Carbon\Carbon;
use Illuminate\Support\Collection;

/**
 * Service responsible for calculating user progress and activity metrics
 * 
 * Handles:
 * - Daily activity calculations
 * - Weekly progress trends
 * - Course completion tracking
 * - Achievement progress
 */
class UserProgressCalculator
{
    /**
     * Get activities completed today for a user
     */
    public function getActivitiesCompletedToday(User $user): int
    {
        return $user->courseEnrollments()
            ->with('course.chapters.items')
            ->get()
            ->flatMap(function ($enrollment) {
                return $enrollment->course->chapters->flatMap->items;
            })
            ->where('is_completed', true)
            ->where('updated_at', '>=', now()->startOfDay())
            ->count();
    }

    /**
     * Get weekly progress trend for a user
     */
    public function getWeeklyProgressTrend(User $user): array
    {
        $startDate = now()->startOfWeek();
        $endDate = now()->endOfWeek();

        return UserEngagementLog::getStudyHoursForPeriod($user, $startDate, $endDate);
    }

    /**
     * Calculate course progress percentage
     */
    public function calculateCourseProgress(User $user, int $courseId): float
    {
        $enrollment = $user->courseEnrollments()->where('course_id', $courseId)->first();
        
        if (!$enrollment) {
            return 0.0;
        }

        $totalItems = $enrollment->course->chapters()->withCount('items')->get()->sum('items_count');
        
        if ($totalItems === 0) {
            return 0.0;
        }

        $completedItems = $enrollment->course->chapters()
            ->with('items')
            ->get()
            ->flatMap->items
            ->where('is_completed', true)
            ->count();

        return round(($completedItems / $totalItems) * 100, 2);
    }

    /**
     * Get user's overall progress across all courses
     */
    public function getOverallProgress(User $user): array
    {
        $enrollments = $user->courseEnrollments()->with('course.chapters.items')->get();
        
        $totalActivitiesCompleted = $enrollments->sum(function ($enrollment) {
            return $enrollment->course->chapters->sum(function ($chapter) {
                return $chapter->items->where('is_completed', true)->count();
            });
        });

        return [
            'total_activities_completed' => $totalActivitiesCompleted,
            'courses_completed' => $enrollments->where('progress', 100)->count(),
            'courses_enrolled' => $enrollments->count(),
            'average_progress' => round($enrollments->avg('progress') ?? 0, 1),
        ];
    }

    /**
     * Check if user meets daily activity requirements
     */
    public function meetsMinimumDailyActivity(User $user, ?Carbon $date = null): bool
    {
        $date = $date ?? Carbon::today();
        $minimumMinutes = config('beaconlms.engagement.minimum_daily_activity_minutes', 30);
        
        $todayStudyMinutes = $user->engagementLogs()
            ->whereDate('date', $date)
            ->sum('study_hours') * 60;

        return $todayStudyMinutes >= $minimumMinutes;
    }

    /**
     * Get study streak information
     */
    public function getStudyStreakInfo(User $user): array
    {
        $currentStreak = $user->current_streak ?? 0;
        $longestStreak = $user->longest_streak ?? 0;
        $streakStartDate = $user->streak_start_date;

        return [
            'current_streak' => $currentStreak,
            'longest_streak' => $longestStreak,
            'streak_start_date' => $streakStartDate,
            'is_streak_active' => $this->meetsMinimumDailyActivity($user),
        ];
    }

    /**
     * Calculate completion rate for a specific time period
     */
    public function getCompletionRate(User $user, Carbon $startDate, Carbon $endDate): float
    {
        $totalDays = $startDate->diffInDays($endDate) + 1;
        $activeDays = $user->engagementLogs()
            ->whereBetween('date', [$startDate, $endDate])
            ->whereRaw('study_hours * 60 >= ?', [config('beaconlms.engagement.minimum_daily_activity_minutes', 30)])
            ->count();

        return $totalDays > 0 ? round(($activeDays / $totalDays) * 100, 1) : 0;
    }
}
