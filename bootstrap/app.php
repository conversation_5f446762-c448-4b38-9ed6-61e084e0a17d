<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withSchedule(function (\Illuminate\Console\Scheduling\Schedule $schedule) {
        // Sync student progress from Moodle every hour
        $schedule->call(function () {
            $students = \App\Models\User::where('role', 'student')
                                      ->whereNotNull('moodle_user_id')
                                      ->get();

            foreach ($students as $student) {
                \App\Jobs\SyncStudentProgress::dispatch($student);
            }
        })
        ->everyFiveMinutes()
        ->name('sync-student-progress')
        ->withoutOverlapping();

        // Clean up old engagement logs weekly
        $schedule->call(function () {
            $cutoffDate = now()->subDays(365);
            \App\Models\UserEngagementLog::where('date', '<', $cutoffDate)->delete();
        })
        ->weekly()
        ->sundays()
        ->at('02:00')
        ->name('cleanup-engagement-logs');
    })
    ->withMiddleware(function (Middleware $middleware): void {
        //
    })
    ->withExceptions(function (Exceptions $exceptions): void {
        //
    })->create();
