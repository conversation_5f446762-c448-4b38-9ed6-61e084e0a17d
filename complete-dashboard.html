<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Beacon FinTrain - Learning Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'dark-blue': '#102649',
                        'light-blue': '#007DB6',
                        'yellow': '#F7C100',
                        'gray-f8': '#F8F9FA',
                        'gray-ee': '#EEE',
                        'gray-b2': '#B2B2B2',
                        'orange-red': '#E54B20',
                        'gray-5': 'rgba(16, 38, 73, 0.05)',
                        'black-40': 'rgba(0, 0, 0, 0.40)',
                    },
                    fontFamily: {
                        'gotham': ['Inter', '-apple-system', 'Roboto', 'Helvetica', 'sans-serif'],
                        'inter': ['Inter', '-apple-system', 'Roboto', 'Helvetica', 'sans-serif'],
                    }
                }
            }
        }
    </script>
    <style>
        .font-gotham { font-family: 'Inter', -apple-system, Roboto, Helvetica, sans-serif; }
        .font-inter { font-family: 'Inter', -apple-system, Roboto, Helvetica, sans-serif; }
    </style>
</head>
<body class="min-h-screen bg-white flex flex-col font-gotham">
    <!-- Main Container -->
    <div class="min-h-screen bg-white flex flex-col">
        <!-- Header -->
        <header class="w-full h-[76px] bg-white shadow-[0_4px_16px_0_rgba(0,0,0,0.10)] flex items-center justify-between px-6">
            <!-- Logo -->
            <div class="flex items-center">
                <div class="flex items-center justify-center h-[60px] w-[126px]">
                    <img
                        src="https://api.builder.io/api/v1/image/assets/TEMP/73eabfc9a05bd5a1edc4faf38fb9480483c7cc05?width=252"
                        alt="Beacon FinTrain Logo"
                        class="w-[126px] h-[50px] object-contain"
                    />
                </div>
            </div>

            <!-- Navigation and User Menu -->
            <div class="flex items-center justify-between w-[927px]">
                <!-- Navigation Links -->
                <div class="flex items-center gap-9 font-gotham text-dark-blue">
                    <button class="text-[16px] font-[350] leading-normal tracking-[-0.16px] hover:text-light-blue transition-colors">
                        Learner
                    </button>
                    <button class="text-[16px] font-[350] leading-normal tracking-[-0.16px] hover:text-light-blue transition-colors">
                        Member Dashboard
                    </button>
                </div>

                <!-- Right Side - Theme, Notifications, Account -->
                <div class="flex items-center gap-4">
                    <!-- Theme Toggle -->
                    <button class="w-6 h-6 flex items-center justify-center hover:bg-gray-f8 rounded transition-colors">
                        <svg class="w-6 h-6 text-dark-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                        </svg>
                    </button>

                    <!-- Notifications -->
                    <div class="relative">
                        <button class="hover:bg-gray-f8 p-1 rounded transition-colors">
                            <svg class="w-6 h-6 text-dark-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h10a2 2 0 002-2V7a2 2 0 00-2-2H4a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                        </button>
                        <div class="absolute -top-1 -right-1 w-3 h-3 bg-orange-red rounded-full"></div>
                    </div>

                    <!-- Account Dropdown -->
                    <div class="flex items-center gap-2 cursor-pointer hover:bg-gray-f8 px-2 py-1 rounded transition-colors">
                        <span class="text-dark-blue font-gotham text-[16px] font-[350] leading-normal tracking-[-0.16px]">
                            My Account
                        </span>
                        <svg class="w-6 h-6 text-dark-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Layout -->
        <div class="flex flex-1">
            <!-- Sidebar -->
            <div class="w-[254px] h-full bg-gray-f8 flex flex-col">
                <div class="flex-1 p-[11px]">
                    <div class="space-y-1">
                        <!-- My Dashboard - Active -->
                        <div class="flex items-center gap-3 px-6 py-3 bg-dark-blue rounded text-gray-ee">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                            </svg>
                            <span class="font-gotham text-[14px] font-[350] leading-normal tracking-[-0.14px]">
                                My Dashboard
                            </span>
                        </div>

                        <!-- My Learning Journey - Expandable -->
                        <div class="space-y-1">
                            <div class="flex items-center justify-between px-6 py-3 text-dark-blue hover:bg-white/50 rounded cursor-pointer">
                                <div class="flex items-center gap-3">
                                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                    </svg>
                                    <span class="font-gotham text-[14px] font-[350] leading-normal tracking-[-0.14px]">
                                        My Learning Journey
                                    </span>
                                </div>
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </div>

                            <!-- Sub-items -->
                            <div class="pl-[60px] space-y-1">
                                <div class="py-2 text-dark-blue font-gotham text-[14px] font-[350] leading-normal tracking-[-0.14px] cursor-pointer hover:bg-white/50 rounded px-4">
                                    In-progress Study Plan
                                </div>
                                <div class="py-2 text-dark-blue font-gotham text-[14px] font-[350] leading-normal tracking-[-0.14px] cursor-pointer hover:bg-white/50 rounded px-4">
                                    Bookmarks & Notes
                                </div>
                                <div class="py-2 text-dark-blue font-gotham text-[14px] font-[350] leading-normal tracking-[-0.14px] cursor-pointer hover:bg-white/50 rounded px-4">
                                    Results & Certificates
                                </div>
                            </div>
                        </div>

                        <!-- Get Certified -->
                        <div class="flex items-center gap-3 px-6 py-3 text-dark-blue hover:bg-white/50 rounded cursor-pointer">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                            </svg>
                            <span class="font-gotham text-[14px] font-[350] leading-normal tracking-[-0.14px]">
                                Get Certified
                            </span>
                        </div>

                        <!-- Resource Center - Expandable -->
                        <div class="space-y-1">
                            <div class="flex items-center justify-between px-6 py-3 text-dark-blue hover:bg-white/50 rounded cursor-pointer">
                                <div class="flex items-center gap-3">
                                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0H8m8 0v2a2 2 0 01-2 2H10a2 2 0 01-2-2V6"></path>
                                    </svg>
                                    <span class="font-gotham text-[14px] font-[350] leading-normal tracking-[-0.14px]">
                                        Resource Center
                                    </span>
                                </div>
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </div>

                            <!-- Sub-items -->
                            <div class="pl-[60px] space-y-1">
                                <div class="py-2 text-dark-blue font-gotham text-[14px] font-[350] leading-normal tracking-[-0.14px] cursor-pointer hover:bg-white/50 rounded px-4">
                                    Skills Assessments
                                </div>
                                <div class="py-2 text-dark-blue font-gotham text-[14px] font-[350] leading-normal tracking-[-0.14px] cursor-pointer hover:bg-white/50 rounded px-4">
                                    Case Study Challenges
                                </div>
                                <div class="py-2 text-dark-blue font-gotham text-[14px] font-[350] leading-normal tracking-[-0.14px] cursor-pointer hover:bg-white/50 rounded px-4">
                                    Games & Activities
                                </div>
                            </div>
                        </div>

                        <!-- Community -->
                        <div class="flex items-center gap-3 px-6 py-3 text-dark-blue hover:bg-white/50 rounded cursor-pointer">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                            </svg>
                            <span class="font-gotham text-[14px] font-[350] leading-normal tracking-[-0.14px]">
                                Community
                            </span>
                        </div>

                        <!-- Ask The Expert -->
                        <div class="flex items-center gap-3 px-6 py-3 text-dark-blue hover:bg-white/50 rounded cursor-pointer">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
                            </svg>
                            <span class="font-gotham text-[16px] font-[350] leading-normal tracking-[-0.16px]">
                                Ask The Expert
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Bottom Section -->
                <div class="p-[11px] space-y-1">
                    <!-- Settings -->
                    <div class="flex items-center gap-3 px-6 py-3 text-dark-blue hover:bg-white/50 rounded cursor-pointer">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        <span class="font-gotham text-[16px] font-[350] leading-normal tracking-[-0.16px]">
                            Settings
                        </span>
                    </div>

                    <!-- Log out -->
                    <div class="flex items-center gap-3 px-6 py-3 bg-white/20 rounded cursor-pointer">
                        <svg class="w-6 h-6 text-orange-red" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                        </svg>
                        <span class="font-inter text-[14px] font-medium leading-normal tracking-[-0.14px] text-orange-red">
                            Log out
                        </span>
                    </div>
                </div>
            </div>

            <!-- Content Area -->
            <div class="flex-1 flex">
                <!-- Main Content -->
                <div class="flex-1 p-6 space-y-3">
                    <!-- Header Text -->
                    <div class="text-dark-blue font-gotham text-[16px] font-normal leading-normal tracking-[-0.16px]">
                        You are enrolled in the
                        <span class="font-bold text-light-blue">CTP Certification</span>
                        Track
                    </div>

                    <!-- Learn Section -->
                    <div class="space-y-2">
                        <div class="flex items-center gap-1">
                            <svg class="w-[18px] h-[18px] text-dark-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                            </svg>
                            <span class="text-dark-blue font-gotham text-[15px] font-[350] capitalize">LEARN</span>
                        </div>

                        <!-- Course Overview Card -->
                        <div class="bg-gray-f8 rounded-xl p-5">
                            <div class="flex gap-6">
                                <div class="flex-1 space-y-4">
                                    <!-- Course Header -->
                                    <div class="flex items-center gap-2">
                                        <span class="text-dark-blue font-gotham text-[14px] font-[350] tracking-[-0.14px]">
                                            INTERACTIVE STUDY PLAN
                                        </span>
                                        <div class="border border-dark-blue rounded px-4 py-1">
                                            <span class="text-dark-blue font-gotham text-[12px] font-[350]">
                                                Get a certificate upon completion
                                            </span>
                                        </div>
                                    </div>

                                    <!-- Course Title -->
                                    <h2 class="text-dark-blue font-gotham text-[20px] font-[350] tracking-[-0.2px]">
                                        CTP Certification Exam Preparation
                                    </h2>

                                    <!-- Action Buttons -->
                                    <div class="flex items-center gap-2">
                                        <button class="bg-yellow rounded px-4 py-2 text-dark-blue font-gotham text-[12px] font-[350] hover:bg-yellow/80 transition-colors">
                                            Continue Learning
                                        </button>
                                        <button class="bg-gray-5 rounded px-4 py-2 text-gray-b2 font-gotham text-[12px] font-[350] hover:bg-gray-5/80 transition-colors">
                                            Get Your Certification
                                        </button>
                                    </div>

                                    <!-- Course Stats -->
                                    <div class="flex flex-wrap items-center gap-3">
                                        <div class="flex items-center gap-1">
                                            <div class="w-6 h-6 bg-white rounded-full flex items-center justify-center">
                                                <div class="w-3 h-3 bg-light-blue"></div>
                                            </div>
                                            <span class="text-dark-blue font-gotham text-[10px] font-[350]">Advanced</span>
                                        </div>
                                        <div class="flex items-center gap-1">
                                            <div class="w-6 h-6 bg-white rounded-full flex items-center justify-center">
                                                <svg class="w-3 h-3 text-light-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                            </div>
                                            <span class="text-dark-blue font-gotham text-[10px] font-[350]">120 Hours</span>
                                        </div>
                                        <div class="flex items-center gap-1">
                                            <div class="w-6 h-6 bg-white rounded-full flex items-center justify-center">
                                                <svg class="w-3 h-3 text-light-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9 5a9 9 0 1118 0H3z"></path>
                                                </svg>
                                            </div>
                                            <span class="text-dark-blue font-gotham text-[10px] font-[350]">90 Videos</span>
                                        </div>
                                        <div class="flex items-center gap-1">
                                            <div class="w-6 h-6 bg-white rounded-full flex items-center justify-center">
                                                <svg class="w-3 h-3 text-light-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                                </svg>
                                            </div>
                                            <span class="text-dark-blue font-gotham text-[10px] font-[350]">30 Quizzes</span>
                                        </div>
                                        <div class="flex items-center gap-1">
                                            <div class="w-6 h-6 bg-white rounded-full flex items-center justify-center">
                                                <svg class="w-3 h-3 text-light-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                                </svg>
                                            </div>
                                            <span class="text-dark-blue font-gotham text-[10px] font-[350]">21070 Learners</span>
                                        </div>
                                        <div class="flex items-center gap-1">
                                            <div class="w-6 h-6 bg-white rounded-full flex items-center justify-center">
                                                <svg class="w-3 h-3 text-light-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                                </svg>
                                            </div>
                                            <span class="text-dark-blue font-gotham text-[10px] font-[350]">Updated last Aug 2023</span>
                                        </div>
                                        <div class="bg-light-blue rounded px-4 py-1">
                                            <span class="text-gray-ee font-gotham text-[10px] font-[350]">4500 XP</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Course Image -->
                                <div class="w-[135px] h-[60px]">
                                    <img
                                        src="https://api.builder.io/api/v1/image/assets/TEMP/92af737f290e8b6cb3702271df12e393751366a7?width=270"
                                        alt="CTP Certification"
                                        class="w-full h-full object-cover rounded-lg"
                                    />
                                </div>
                            </div>
                        </div>

                        <!-- Chapter 1 - Expanded -->
                        <div class="bg-gray-f8 rounded-xl p-4">
                            <div class="flex items-start gap-2">
                                <div class="w-6 h-6 bg-dark-blue rounded-full flex items-center justify-center text-gray-ee font-gotham text-[15px] font-bold">
                                    1
                                </div>
                                <div class="flex-1 space-y-3">
                                    <div class="flex items-center justify-between">
                                        <span class="text-dark-blue font-gotham text-[15px] font-[350] tracking-[-0.15px]">
                                            CHAPTER 1: Role of Treasury Management
                                        </span>
                                        <svg class="w-6 h-6 text-dark-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
                                        </svg>
                                    </div>

                                    <div class="flex items-center gap-4">
                                        <div class="flex-1 h-[5px] bg-white rounded-full relative">
                                            <div class="w-[66px] h-[5px] bg-yellow rounded-full"></div>
                                        </div>
                                        <span class="text-gray-b2 font-gotham text-[10px] font-[350]">40%</span>
                                    </div>

                                    <p class="text-dark-blue font-gotham text-[12px] font-[325] leading-4 tracking-[-0.12px]">
                                        This chapter discusses the basic role and objectives of treasury management, the potential
                                        organizational structures for treasury, and the relationship between treasury management and
                                        corporate financial management.
                                    </p>

                                    <!-- Skip Test Card -->
                                    <div class="border border-dark-blue/20 bg-white rounded p-4 space-y-3">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center gap-3">
                                                <svg class="w-4 h-4 text-dark-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                                </svg>
                                                <span class="text-dark-blue font-gotham text-[12px] font-[350]">
                                                    Already know this chapter ?
                                                </span>
                                            </div>
                                            <button class="bg-dark-blue text-gray-ee rounded px-4 py-2 font-gotham text-[12px] font-[350] hover:bg-dark-blue/80 transition-colors">
                                                Take the test
                                            </button>
                                        </div>
                                        <p class="text-dark-blue font-gotham text-[12px] font-[325] leading-4 tracking-[-0.12px]">
                                            Take a quick test to check if you can skip the chapter.
                                        </p>
                                    </div>

                                    <!-- Chapter Content List -->
                                    <div class="bg-white rounded p-4 space-y-4">
                                        <div class="flex items-center justify-between">
                                            <div class="text-light-blue font-gotham text-[12px] font-[350] underline cursor-pointer hover:text-light-blue/80">
                                                hide chapter details
                                            </div>
                                            <button class="bg-yellow text-dark-blue rounded px-4 py-2 font-gotham text-[12px] font-[350] hover:bg-yellow/80 transition-colors">
                                                Start Chapter
                                            </button>
                                        </div>

                                        <!-- Content Items -->
                                        <div class="space-y-3">
                                            <div class="flex items-center justify-between">
                                                <div class="flex items-center gap-2">
                                                    <svg class="w-4 h-4 text-dark-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9 5a9 9 0 1118 0H3z"></path>
                                                    </svg>
                                                    <span class="text-dark-blue font-gotham text-[12px] font-[350]">Introduction Video</span>
                                                </div>
                                                <div class="flex items-center gap-1">
                                                    <span class="text-dark-blue font-gotham text-[12px] font-[350]">50 XP</span>
                                                    <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                    </svg>
                                                </div>
                                            </div>
                                            <div class="flex items-center justify-between">
                                                <div class="flex items-center gap-2">
                                                    <svg class="w-4 h-4 text-dark-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9 5a9 9 0 1118 0H3z"></path>
                                                    </svg>
                                                    <span class="text-dark-blue font-gotham text-[12px] font-[350]">What is financial statement?</span>
                                                </div>
                                                <div class="flex items-center gap-1">
                                                    <span class="text-dark-blue font-gotham text-[12px] font-[350]">50 XP</span>
                                                    <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                    </svg>
                                                </div>
                                            </div>
                                            <div class="flex items-center justify-between">
                                                <div class="flex items-center gap-2">
                                                    <svg class="w-4 h-4 text-dark-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                                    </svg>
                                                    <span class="text-dark-blue font-gotham text-[12px] font-[350]">Quiz 01</span>
                                                </div>
                                                <div class="flex items-center gap-1">
                                                    <span class="text-dark-blue font-gotham text-[12px] font-[350]">80 XP</span>
                                                    <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                    </svg>
                                                </div>
                                            </div>
                                            <div class="flex items-center justify-between">
                                                <div class="flex items-center gap-2">
                                                    <svg class="w-4 h-4 text-dark-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9 5a9 9 0 1118 0H3z"></path>
                                                    </svg>
                                                    <span class="text-dark-blue font-gotham text-[12px] font-[350]">Why learning financial statement?</span>
                                                </div>
                                                <div class="flex items-center gap-1">
                                                    <span class="text-dark-blue font-gotham text-[12px] font-[350]">50 XP</span>
                                                </div>
                                            </div>
                                            <div class="flex items-center justify-between">
                                                <div class="flex items-center gap-2">
                                                    <svg class="w-4 h-4 text-dark-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                                    </svg>
                                                    <span class="text-dark-blue font-gotham text-[12px] font-[350]">Chapter Summary</span>
                                                </div>
                                                <div class="flex items-center gap-1">
                                                    <span class="text-dark-blue font-gotham text-[12px] font-[350]">70 XP</span>
                                                </div>
                                            </div>
                                            <div class="flex items-center justify-between">
                                                <div class="flex items-center gap-2">
                                                    <svg class="w-4 h-4 text-dark-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                                    </svg>
                                                    <span class="text-dark-blue font-gotham text-[12px] font-[350]">Final chapter exam</span>
                                                </div>
                                                <div class="flex items-center gap-1">
                                                    <span class="text-dark-blue font-gotham text-[12px] font-[350]">150 XP</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Other Chapters - Collapsed -->
                        <div class="bg-gray-f8 rounded-xl p-4">
                            <div class="flex items-center gap-2">
                                <div class="w-6 h-6 bg-dark-blue rounded-full flex items-center justify-center text-gray-ee font-gotham text-[15px] font-bold">
                                    2
                                </div>
                                <div class="flex items-center justify-between flex-1">
                                    <span class="text-dark-blue font-gotham text-[15px] font-[350] tracking-[-0.15px]">
                                        CHAPTER 2: Legal, Regulatory, and Tax Environment
                                    </span>
                                    <svg class="w-6 h-6 text-dark-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <div class="bg-gray-f8 rounded-xl p-4">
                            <div class="flex items-center gap-2">
                                <div class="w-6 h-6 bg-dark-blue rounded-full flex items-center justify-center text-gray-ee font-gotham text-[15px] font-bold">
                                    3
                                </div>
                                <div class="flex items-center justify-between flex-1">
                                    <span class="text-dark-blue font-gotham text-[15px] font-[350] tracking-[-0.15px]">
                                        CHAPTER 3: Banks and Financial Institutions
                                    </span>
                                    <svg class="w-6 h-6 text-dark-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <div class="bg-gray-f8 rounded-xl p-4">
                            <div class="flex items-center gap-2">
                                <div class="w-6 h-6 bg-dark-blue rounded-full flex items-center justify-center text-gray-ee font-gotham text-[15px] font-bold">
                                    4
                                </div>
                                <div class="flex items-center justify-between flex-1">
                                    <span class="text-dark-blue font-gotham text-[15px] font-[350] tracking-[-0.15px]">
                                        CHAPTER 4: Payment Instruments and Systems
                                    </span>
                                    <svg class="w-6 h-6 text-dark-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <div class="bg-gray-f8 rounded-xl p-4">
                            <div class="flex items-center gap-2">
                                <div class="w-6 h-6 bg-dark-blue rounded-full flex items-center justify-center text-gray-ee font-gotham text-[15px] font-bold">
                                    5
                                </div>
                                <div class="flex items-center justify-between flex-1">
                                    <span class="text-dark-blue font-gotham text-[15px] font-[350] tracking-[-0.15px]">
                                        CHAPTER 5: Money Markets
                                    </span>
                                    <svg class="w-6 h-6 text-dark-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <!-- Explore More Section -->
                        <div class="space-y-2 mt-8">
                            <span class="text-dark-blue font-gotham text-[15px] font-[350]">Explore More</span>

                            <div class="bg-gray-f8 rounded-xl p-3">
                                <img
                                    src="https://api.builder.io/api/v1/image/assets/TEMP/a1a257cba01948cc9974fb95363e34ee3bd542a0?width=334"
                                    alt="Future of Finance"
                                    class="w-[167px] h-[65px] object-contain mb-3"
                                />
                                <div class="flex items-center justify-between">
                                    <span class="text-dark-blue font-gotham text-[14px] font-[350] tracking-[-0.14px]">
                                        Connect, learn, and grow at Future of Finance MEA | Cairo 2025
                                    </span>
                                    <button class="bg-dark-blue text-gray-ee rounded px-4 py-2 font-gotham text-[12px] font-[350] hover:bg-dark-blue/80 transition-colors">
                                        Register now
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Profile Section -->
                <div class="w-[361px] flex flex-col gap-2">
                    <!-- Profile Card -->
                    <div class="bg-gray-f8 rounded-xl p-3">
                        <div class="flex items-start gap-3">
                            <img
                                src="https://api.builder.io/api/v1/image/assets/TEMP/18f15db93a7547e7e23f3da5b42793f5f7d1e05b?width=126"
                                alt="Profile"
                                class="w-[63px] h-[63px] rounded-lg object-cover"
                            />
                            <div class="flex-1 space-y-1">
                                <div class="text-dark-blue font-gotham text-[12px] font-[325] capitalize">
                                    Welcome,
                                </div>
                                <div class="text-light-blue font-gotham text-[12px] font-[350] capitalize">
                                    Marian Nader
                                </div>
                                <div class="flex items-center justify-between text-dark-blue font-gotham text-[10px] font-[325] capitalize cursor-pointer hover:text-light-blue">
                                    <span>complete your profile</span>
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                    </svg>
                                </div>
                                <div class="flex items-center gap-2">
                                    <div class="flex-1 h-[5px] bg-white rounded-full relative">
                                        <div class="w-[30px] h-[3px] bg-yellow rounded-full absolute top-[1px] left-[1px]"></div>
                                    </div>
                                    <span class="text-gray-b2 font-gotham text-[10px] font-[350] tracking-[-0.1px]">25%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Stats Cards Row -->
                    <div class="grid grid-cols-2 gap-1">
                        <!-- Daily Streak -->
                        <div class="bg-gray-f8 rounded-xl p-3">
                            <div class="flex items-center gap-1 mb-2">
                                <span class="text-dark-blue font-gotham text-[12px] font-[350] capitalize">Daily Streak</span>
                                <svg class="w-3 h-3 text-light-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div class="flex items-center gap-2">
                                <div class="w-5 h-5 bg-white border border-yellow rounded-full flex items-center justify-center">
                                    <svg class="w-3 h-3 text-yellow fill-yellow" fill="currentColor" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                    </svg>
                                </div>
                                <span class="text-dark-blue font-gotham text-[12px] font-[350] capitalize">2 Days</span>
                            </div>
                        </div>

                        <!-- CTP Exam -->
                        <div class="bg-gray-f8 rounded-xl p-3">
                            <div class="flex items-center gap-1 mb-2">
                                <span class="text-dark-blue font-gotham text-[12px] font-[350] capitalize">Your CTP Exam</span>
                                <svg class="w-3 h-3 text-light-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div class="space-y-1">
                                <div class="flex items-baseline gap-1">
                                    <span class="text-dark-blue font-gotham text-[20px] font-[350]">43</span>
                                    <span class="text-dark-blue font-gotham text-[12px] font-[350] capitalize"> Days Left</span>
                                </div>
                                <div class="text-light-blue font-gotham text-[10px] font-[325] underline capitalize cursor-pointer hover:text-light-blue/80">
                                    Update the date
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Enrolled Track -->
                    <div class="bg-gray-f8 rounded-xl p-4">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center gap-1">
                                <span class="text-dark-blue font-gotham text-[15px] font-[350] capitalize">Enrolled Track</span>
                                <svg class="w-4 h-4 text-dark-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </div>
                            <div class="bg-gray-ee rounded px-4 py-1">
                                <span class="text-light-blue font-gotham text-[10px] font-[350] underline cursor-pointer hover:text-light-blue/80">View Details</span>
                            </div>
                        </div>

                        <div class="space-y-3">
                            <div class="text-light-blue font-gotham text-[14px] font-[350] capitalize">
                                CTP Certification Preparation
                            </div>
                            <div class="space-y-1">
                                <div class="w-full h-[5px] bg-white rounded-full relative">
                                    <div class="w-[33px] h-[5px] bg-yellow rounded-full"></div>
                                </div>
                                <span class="text-dark-blue font-gotham text-[10px] font-[350] capitalize">
                                    90/120 Hours to complete
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Total Engagement Chart -->
                    <div class="bg-gray-f8 rounded-xl p-4">
                        <div class="flex items-center gap-1 mb-4">
                            <span class="text-dark-blue font-gotham text-[15px] font-[350] capitalize">Total Engagement</span>
                            <svg class="w-4 h-4 text-light-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>

                        <!-- Chart Area -->
                        <div class="relative h-[212px] border border-gray-300 rounded-lg bg-white p-4">
                            <!-- Y-axis labels -->
                            <div class="absolute left-2 top-0 h-full flex flex-col justify-between text-gray-400 text-xs py-4">
                                <span>+4 Hr</span>
                                <span>2 Hr</span>
                                <span>60 Min</span>
                                <span>30 Min</span>
                                <span>0 Min</span>
                            </div>

                            <!-- Bars -->
                            <div class="ml-8 h-full flex items-end justify-center gap-6 pb-6">
                                <div class="flex flex-col items-center">
                                    <div class="w-8 bg-light-blue rounded-t-lg rounded-b-lg mb-2" style="height: 102px;"></div>
                                    <span class="text-gray-400 text-xs">Sun</span>
                                </div>
                                <div class="flex flex-col items-center">
                                    <div class="w-8 bg-light-blue rounded-t-lg rounded-b-lg mb-2" style="height: 62px;"></div>
                                    <span class="text-gray-400 text-xs">Mon</span>
                                </div>
                                <div class="flex flex-col items-center">
                                    <div class="w-8 bg-light-blue rounded-t-lg rounded-b-lg mb-2" style="height: 111px;"></div>
                                    <span class="text-gray-400 text-xs">Tues</span>
                                </div>
                                <div class="flex flex-col items-center">
                                    <div class="w-8 bg-light-blue rounded-t-lg rounded-b-lg mb-2" style="height: 79px;"></div>
                                    <span class="text-gray-400 text-xs">Wed</span>
                                </div>
                                <div class="flex flex-col items-center">
                                    <div class="w-8 bg-light-blue rounded-t-lg rounded-b-lg mb-2" style="height: 38px;"></div>
                                    <span class="text-gray-400 text-xs">Thurs</span>
                                </div>
                            </div>

                            <!-- Grid lines -->
                            <div class="absolute inset-0 flex flex-col justify-between py-4 ml-8 pointer-events-none">
                                <div class="border-t border-dashed border-gray-200"></div>
                                <div class="border-t border-dashed border-gray-200"></div>
                                <div class="border-t border-dashed border-gray-200"></div>
                                <div class="border-t border-dashed border-gray-200"></div>
                                <div class="border-t border-dashed border-gray-200"></div>
                            </div>

                            <!-- Legend -->
                            <div class="absolute top-4 right-4 flex items-center gap-2 bg-gray-200 rounded px-2 py-1">
                                <div class="w-2 h-2 bg-light-blue rounded-sm"></div>
                                <span class="text-gray-600 text-xs">90 Min</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <footer class="w-full min-w-[800px] flex items-center justify-between px-8 py-4 border-t border-gray-200">
            <!-- Copyright -->
            <div class="flex items-center gap-1">
                <svg class="w-[14px] h-[14px] text-gray-b2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12a3 3 0 100-6 3 3 0 000 6zm0 0a8.5 8.5 0 004.7-1.4M9 12c-2.5 0-4.7-1.4-4.7-1.4"></path>
                </svg>
                <span class="text-gray-b2 font-gotham text-[10px] font-[325]">
                    2025 Beacon FinTrain
                </span>
            </div>

            <!-- Links -->
            <div class="flex items-center gap-4">
                <a href="#" class="text-black-40 font-inter text-[12px] font-normal leading-4 hover:text-dark-blue transition-colors">
                    About
                </a>
                <a href="#" class="text-black-40 font-inter text-[12px] font-normal leading-4 hover:text-dark-blue transition-colors">
                    Support
                </a>
                <a href="#" class="text-black-40 font-inter text-[12px] font-normal leading-4 hover:text-dark-blue transition-colors">
                    Contact Us
                </a>
            </div>
        </footer>
    </div>


</body>
</html>
