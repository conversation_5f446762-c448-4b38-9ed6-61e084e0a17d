<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Moodle Integration Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for connecting to Moodle LMS via web services
    |
    */
    'moodle' => [
        'url' => env('MOODLE_URL', 'http://localhost/webservice/rest/server.php'),
        'token' => env('MOODLE_TOKEN', ''),
        'service' => env('MOODLE_SERVICE', 'moodle_mobile_app'),
        'timeout' => env('MOODLE_TIMEOUT', 30),
    ],

    'stores' => [
        'database' => [
            'driver' => 'database',
            'table' => 'cache',
            'connection' => null,
            'lock_connection' => null,
        ],
    ],

// BeaconLMS specific settings
    'cache_ttl' => [
        'active_user' => 60,     // 1 minute for active users
        'inactive_user' => 300,  // 5 minutes for inactive users
        'stats_data' => 180,     // 3 minutes for statistics
        'progress_data' => 120,  // 2 minutes for progress
    ],

    /*
    |--------------------------------------------------------------------------
    | SSO Configuration
    |--------------------------------------------------------------------------
    |
    | Settings for Single Sign-On integration with Moodle
    |
    */
    'sso' => [
        'shared_secret' => env('BEACON_SSO_SECRET', ''),
        'token_expiry_minutes' => env('BEACON_SSO_TOKEN_EXPIRY', 5),
    ],

    /*
    |--------------------------------------------------------------------------
    | CTP Course Configuration
    |--------------------------------------------------------------------------
    |
    | Settings specific to the CTP Finance Certification course
    |
    */
    'ctp_course' => [
        'id' => (int)env('CTP_COURSE_ID', 2),
        'exam_preparation_days' => (int)env('CTP_EXAM_PREPARATION_DAYS', 43),
        'title' => 'CTP Finance Certification',
        'description' => 'Certified Treasury Professional Finance certification course',
        'chapters' => [
            'Role of Treasury Management',
            'Legal, Regulatory, and Tax Environment',
            'Banks and Financial Institutions',
            'Payment Instruments and Systems',
            'Money Markets'
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | XP Points System
    |--------------------------------------------------------------------------
    |
    | Point values for different learning activities
    |
    */
    'xp_points' => [
        'video_completion' => env('XP_VIDEO_COMPLETION', 20),
        'quiz_passed' => env('XP_QUIZ_PASSED', 50),
        'assignment_submitted' => env('XP_ASSIGNMENT_SUBMITTED', 40),
        'chapter_completion' => env('XP_CHAPTER_COMPLETION', 100),
        'certification_required' => env('XP_CERTIFICATION_REQUIRED', 1500),
    ],

    /*
    |--------------------------------------------------------------------------
    | Engagement Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for tracking student engagement and streaks
    |
    */
    'engagement' => [
        'minimum_daily_activity_minutes' => env('MINIMUM_DAILY_ACTIVITY_MINUTES', 30),
        'weekend_streak_multiplier' => env('WEEKEND_STREAK_MULTIPLIER', 2),
        'progress_cache_ttl' => env('PROGRESS_CACHE_TTL', 300), // 5 minutes
        'sync_interval_minutes' => 5,
    ],

    /*
    |--------------------------------------------------------------------------
    | File Upload Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for profile photos and other uploads
    |
    */
    'uploads' => [
        'profile_photo' => [
            'max_size' => env('MAX_PROFILE_PHOTO_SIZE', 2048), // KB
            'allowed_extensions' => explode(',', env('ALLOWED_PHOTO_EXTENSIONS', 'jpg,jpeg,png,gif')),
            'disk' => 'public',
            'path' => 'profile-photos',
        ],
        'course_thumbnails' => [
            'max_size' => 5120, // 5MB
            'allowed_extensions' => ['jpg', 'jpeg', 'png', 'webp'],
            'disk' => 'public',
            'path' => 'course-thumbnails',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Dashboard Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for the student dashboard appearance and behavior
    |
    */
    'dashboard' => [
        'colors' => [
            'primary' => '#3B82F6',
            'success' => '#10B981',
            'warning' => '#F59E0B',
            'background' => '#F3F4F6',
            'text' => '#1F2937',
        ],
        'chart' => [
            'days_to_show' => 7,
            'max_hours_display' => 5,
        ],
        'notifications' => [
            'exam_reminder_days' => [30, 14, 7, 3, 1],
            'streak_milestone_days' => [7, 14, 30, 60, 100],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Business Rules
    |--------------------------------------------------------------------------
    |
    | Core business logic configuration
    |
    */
    'business_rules' => [
        'chapter_unlock_threshold' => 80, // Percentage required to unlock next chapter
        'max_exam_reschedules' => 3,
        'minimum_preparation_days' => 30,
        'streak_reset_hours' => 24,
        'progress_sync_batch_size' => 50,
    ],

    /*
    |--------------------------------------------------------------------------
    | API Rate Limiting
    |--------------------------------------------------------------------------
    |
    | Rate limiting configuration for Moodle API calls
    |
    */
    'rate_limiting' => [
        'moodle_api_calls_per_minute' => 60,
        'progress_sync_calls_per_minute' => 30,
        'user_creation_calls_per_minute' => 10,
    ],
];
