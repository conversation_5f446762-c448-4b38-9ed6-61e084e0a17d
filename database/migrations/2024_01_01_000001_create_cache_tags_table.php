<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cache_tags', function (Blueprint $table) {
            $table->id();
            $table->string('tag', 100)->index();
            $table->string('cache_key', 255)->index();
            $table->timestamp('expires_at')->index();
            $table->timestamps();
            
            // Composite index for efficient tag-based lookups
            $table->index(['tag', 'expires_at']);
            $table->index(['cache_key', 'expires_at']);
            
            // Unique constraint to prevent duplicate tag-key combinations
            $table->unique(['tag', 'cache_key']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cache_tags');
    }
};
