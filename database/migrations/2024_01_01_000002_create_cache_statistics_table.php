<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cache_statistics', function (Blueprint $table) {
            $table->id();
            $table->date('date')->index();
            $table->tinyInteger('hour')->index(); // 0-23
            $table->bigInteger('hits')->default(0);
            $table->bigInteger('misses')->default(0);
            $table->timestamps();
            
            // Unique constraint for date-hour combination
            $table->unique(['date', 'hour']);
            
            // Index for efficient time-based queries
            $table->index(['date', 'hour']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cache_statistics');
    }
};
