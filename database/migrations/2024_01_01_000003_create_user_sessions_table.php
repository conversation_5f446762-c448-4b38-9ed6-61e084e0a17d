<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_sessions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('session_key', 255)->unique();
            $table->string('activity_type', 100)->index();
            $table->timestamp('started_at')->index();
            $table->timestamp('last_activity_at')->nullable();
            $table->json('data')->nullable();
            $table->boolean('is_active')->default(true)->index();
            $table->timestamps();

            // Indexes for efficient queries
            $table->index(['user_id', 'is_active']);
            $table->index(['user_id', 'last_activity_at']);
            $table->index(['is_active', 'last_activity_at']);
            $table->index(['started_at', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_sessions');
    }
};
