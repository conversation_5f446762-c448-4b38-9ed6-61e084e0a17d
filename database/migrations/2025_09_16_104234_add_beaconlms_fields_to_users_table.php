<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Moodle integration fields
            $table->unsignedBigInteger('moodle_user_id')->nullable()->after('email');
            $table->enum('role', ['admin', 'student'])->default('student')->after('moodle_user_id');
            
            // Profile and engagement fields
            $table->string('profile_photo')->nullable()->after('role');
            $table->unsignedInteger('current_streak')->default(0)->after('profile_photo');
            $table->date('last_activity_date')->nullable()->after('current_streak');
            $table->timestamp('last_synced_at')->nullable()->after('last_activity_date');
            
            // Add indexes for performance
            $table->index('moodle_user_id');
            $table->index('role');
            $table->index('last_activity_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex(['moodle_user_id']);
            $table->dropIndex(['role']);
            $table->dropIndex(['last_activity_date']);
            
            $table->dropColumn([
                'moodle_user_id',
                'role',
                'profile_photo',
                'current_streak',
                'last_activity_date',
                'last_synced_at'
            ]);
        });
    }
};
