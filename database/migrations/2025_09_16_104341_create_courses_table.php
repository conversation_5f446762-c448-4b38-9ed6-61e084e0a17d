<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('courses', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('description');
            $table->unsignedBigInteger('moodle_course_id')->unique();
            $table->string('thumbnail')->nullable();
            $table->unsignedInteger('duration_days')->default(30);
            $table->unsignedInteger('total_hours')->default(0);
            $table->unsignedInteger('total_videos')->default(0);
            $table->unsignedInteger('total_quizzes')->default(0);
            $table->timestamps();
            
            // Add indexes for performance
            $table->index('moodle_course_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('courses');
    }
};
