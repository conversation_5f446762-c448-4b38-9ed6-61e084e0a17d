<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('course_enrollments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('course_id')->constrained()->onDelete('cascade');
            $table->unsignedBigInteger('moodle_enrollment_id')->nullable();
            $table->decimal('progress', 5, 2)->default(0.00);
            $table->timestamp('enrolled_at');
            $table->timestamp('completed_at')->nullable();
            $table->date('exam_date');
            $table->timestamps();
            
            // Add indexes for performance
            $table->index(['user_id', 'course_id']);
            $table->index('moodle_enrollment_id');
            $table->index('exam_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('course_enrollments');
    }
};
