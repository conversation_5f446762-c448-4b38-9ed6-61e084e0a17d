<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chapter_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('course_chapter_id')->constrained()->onDelete('cascade');
            $table->string('title');
            $table->enum('type', ['video', 'quiz', 'assignment', 'resource']);
            $table->unsignedBigInteger('moodle_activity_id')->nullable();
            $table->unsignedInteger('xp_points')->default(0);
            $table->boolean('is_completed')->default(false);
            $table->unsignedInteger('order');
            $table->unsignedInteger('duration_minutes')->nullable();
            $table->unsignedInteger('passing_score')->nullable();
            $table->timestamps();
            
            // Add indexes for performance
            $table->index(['course_chapter_id', 'order']);
            $table->index('moodle_activity_id');
            $table->index('type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chapter_items');
    }
};
