<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use App\Models\User;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create default admin user
        User::create([
            'name' => 'BeaconLMS Admin',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => Hash::make('123456'),
            'role' => 'admin',
            'moodle_user_id' => null, // Admin doesn't need Moodle sync
            'current_streak' => 0,
            'last_activity_date' => now()->toDateString(),
            'last_synced_at' => now(),
        ]);

        // Create a sample student user for testing
        User::create([
            'name' => '<PERSON> Do<PERSON>',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => Hash::make('123456'),
            'role' => 'student',
            'moodle_user_id' => null, // Will be set when synced with Moodle
            'current_streak' => 5,
            'last_activity_date' => now()->toDateString(),
            'last_synced_at' => null,
        ]);
    }
}
