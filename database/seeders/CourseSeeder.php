<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class CourseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create the CTP Finance Certification course
        $courseId = DB::table('courses')->insertGetId([
            'title' => 'CTP Finance Certification',
            'description' => 'Certified Treasury Professional Finance certification course covering all essential topics for treasury management professionals.',
            'moodle_course_id' => 1, // This should match the actual Moodle course ID
            'thumbnail' => 'course-thumbnails/ctp-finance.jpg',
            'duration_days' => 43,
            'total_hours' => 120,
            'total_videos' => 45,
            'total_quizzes' => 15,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ]);

        // Create course chapters
        $chapters = [
            ['title' => 'Role of Treasury Management', 'order' => 1, 'moodle_section_id' => 1],
            ['title' => 'Legal, Regulatory, and Tax Environment', 'order' => 2, 'moodle_section_id' => 2],
            ['title' => 'Banks and Financial Institutions', 'order' => 3, 'moodle_section_id' => 3],
            ['title' => 'Payment Instruments and Systems', 'order' => 4, 'moodle_section_id' => 4],
            ['title' => 'Money Markets', 'order' => 5, 'moodle_section_id' => 5],
        ];

        foreach ($chapters as $chapter) {
            $chapterId = DB::table('course_chapters')->insertGetId([
                'course_id' => $courseId,
                'title' => $chapter['title'],
                'order' => $chapter['order'],
                'moodle_section_id' => $chapter['moodle_section_id'],
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]);

            // Create sample chapter items for each chapter
            $this->createChapterItems($chapterId, $chapter['order']);
        }
    }

    /**
     * Create sample chapter items for a chapter
     */
    private function createChapterItems($chapterId, $chapterOrder): void
    {
        $items = [
            [
                'title' => 'Introduction Video',
                'type' => 'video',
                'xp_points' => 20,
                'order' => 1,
                'duration_minutes' => 15,
                'moodle_activity_id' => ($chapterOrder * 10) + 1,
            ],
            [
                'title' => 'Core Concepts Video',
                'type' => 'video',
                'xp_points' => 20,
                'order' => 2,
                'duration_minutes' => 25,
                'moodle_activity_id' => ($chapterOrder * 10) + 2,
            ],
            [
                'title' => 'Practice Quiz',
                'type' => 'quiz',
                'xp_points' => 50,
                'order' => 3,
                'passing_score' => 80,
                'moodle_activity_id' => ($chapterOrder * 10) + 3,
            ],
            [
                'title' => 'Case Study Assignment',
                'type' => 'assignment',
                'xp_points' => 40,
                'order' => 4,
                'moodle_activity_id' => ($chapterOrder * 10) + 4,
            ],
            [
                'title' => 'Chapter Resources',
                'type' => 'resource',
                'xp_points' => 0,
                'order' => 5,
                'moodle_activity_id' => ($chapterOrder * 10) + 5,
            ],
        ];

        foreach ($items as $item) {
            DB::table('chapter_items')->insert([
                'course_chapter_id' => $chapterId,
                'title' => $item['title'],
                'type' => $item['type'],
                'moodle_activity_id' => $item['moodle_activity_id'],
                'xp_points' => $item['xp_points'],
                'order' => $item['order'],
                'duration_minutes' => $item['duration_minutes'] ?? null,
                'passing_score' => $item['passing_score'] ?? null,
                'is_completed' => false,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]);
        }
    }
}
