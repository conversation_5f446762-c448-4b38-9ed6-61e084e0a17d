# BeaconLMS SSO Plugin: Detailed Documentation

This document provides a comprehensive, expert-level explanation of the BeaconLMS SSO authentication plugin for Mo<PERSON><PERSON>.

---

## How It Works: The SSO Flow

The SSO process is based on a secure, token-based authentication mechanism. A **Shared Secret** is configured in both BeaconLMS and Moodle to ensure that all SSO requests are legitimate and have not been tampered with.

**The step-by-step process is as follows:**

1.  **User Initiates SSO**: The user clicks the "Launch Moodle" button within the BeaconLMS application.
2.  **Token Generation (BeaconLMS)**: BeaconLMS generates a secure, one-time-use token. This token is a `SHA-256` hash created from the user's details, a timestamp, a random string (nonce), and the **Shared Secret**.
3.  **Redirection to <PERSON><PERSON><PERSON>**: BeaconLMS redirects the user's browser to the Moodle SSO endpoint (`/auth/beaconlms/sso.php`), sending the user's details and the secure token as URL parameters.
4.  **Token Validation (Moodle)**: The Moodle plugin receives the request. It re-creates the token on its side using the same data and the **Shared Secret** stored in its configuration.
5.  **Security Check**: The plugin compares the token sent by BeaconLMS with the token it just generated. If they match, the request is considered authentic.
6.  **Login and Session Creation**: If the token is valid, the plugin finds the corresponding user in the Moodle database, creates a new login session for them, and redirects them to their Moodle dashboard.

### SSO Sequence Diagram

This diagram illustrates the interaction between the user, BeaconLMS, and Moodle during the SSO process.

```mermaid
sequenceDiagram
    actor User
    participant BeaconLMS
    participant Moodle

    User->>BeaconLMS: Clicks "Launch Moodle" button
    BeaconLMS->>BeaconLMS: 1. Generate secure token (SHA256) using user data + shared secret
    BeaconLMS->>User: 2. Redirect to Moodle SSO URL with token
    User->>Moodle: 3. Browser follows redirect to /auth/beaconlms/sso.php
    Moodle->>Moodle: 4. sso.php receives request
    Moodle->>Moodle: 5. auth.php validates token (re-generates and compares)
    alt Token is Valid
        Moodle->>Moodle: 6. Find user in Moodle DB
        Moodle->>Moodle: 7. Create login session for user
        Moodle->>User: 8. Redirect to Moodle Dashboard (/my)
    else Token is Invalid
        Moodle->>User: Redirect to Login page with error
    end
```

---

## File-by-File Breakdown

*   **`auth.php` (The Core Engine)**: The main plugin class. Its most important job is to perform the critical security check by validating the SSO token.
*   **`sso.php` (The Front Door)**: The public URL that receives the SSO request, calls the validation logic, and logs the user in.
*   **`settings.php` (The Admin Control Panel)**: Creates the settings page in the Moodle admin area using the modern Moodle Settings API.
*   **`lang/en/auth_beaconlms.php` (The Translator)**: Contains all the text strings for the plugin's user interface, making it translatable.
*   **`version.php` (The ID Card)**: Provides Moodle with essential metadata about the plugin (version, requirements, etc.).
*   **`README.md` (The Quick-Start Guide)**: Provides concise installation and configuration instructions.

---

## Key Security Features

-   ✅ **Secure Token Validation**: Uses `SHA-256` hashing with a shared secret to prevent unauthorized access.
-   ✅ **Token Expiry**: Tokens are only valid for a short, configurable period (default: 5 minutes) to prevent old tokens from being used.
-   ✅ **Nonce Protection**: A unique, random string (nonce) is included in each request to prevent replay attacks.
-   ✅ **User Validation**: The plugin verifies that the user exists in Moodle and is not suspended before creating a session.
