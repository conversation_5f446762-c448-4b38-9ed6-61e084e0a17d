# BeaconLMS SSO Authentication Plugin for Moodle

This plugin provides seamless and secure Single Sign-On (SSO) integration between the BeaconLMS platform and a Moodle instance.

**For a detailed explanation of the SSO flow, architecture, and security features, please see [DOCUMENTATION.md](DOCUMENTATION.md).**

---

## Quick Installation Guide

### Step 1: Copy Plugin to <PERSON><PERSON>le

<PERSON><PERSON> the `moodle-auth-plugin` directory to your Moodle installation's authentication folder. **You must rename the folder to `beaconlms`**.

-   **Source**: `moodle-auth-plugin/`
-   **Destination**: `/path/to/moodle/auth/beaconlms/`

### Step 2: Install via Moodle Admin

1.  Log into <PERSON><PERSON><PERSON> as an administrator.
2.  Navigate to **Site Administration > Notifications**.
3.  <PERSON><PERSON><PERSON> will detect the new plugin. Click **"Upgrade Moodle database now"**.

### Step 3: Configure the Plugin

1.  Navigate to **Site Administration > Plugins > Authentication > Manage authentication**.
2.  Find **"BeaconLMS SSO"** and click its **Settings** link.
3.  Set your **Shared Secret** (this must match the secret in BeaconLMS).
4.  Click **Save changes**.

### Step 4: Enable the Plugin

1.  Return to **Manage authentication**.
2.  Click the **Enable** icon (the eye symbol) next to **"BeaconLMS SSO"**.

---

## Troubleshooting

-   **`Invalid token`**: The **Shared Secret** does not match between BeaconLMS and Moodle.
-   **`User not found`**: The user has not been created in Moodle yet.
-   **`404 Not Found`**: The plugin is in the wrong directory. The path must be `/auth/beaconlms/`.
