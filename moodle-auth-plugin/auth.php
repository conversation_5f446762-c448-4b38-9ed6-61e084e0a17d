<?php
// This file is part of Moodle - http://moodle.org/
//
// Moodle is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.

/**
 * BeaconLMS SSO Authentication Plugin
 *
 * @package    auth_beaconlms
 * @copyright  2025 BeaconLMS
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

require_once($CFG->libdir.'/authlib.php');

/**
 * BeaconLMS SSO authentication plugin.
 */
class auth_plugin_beaconlms extends auth_plugin_base {

    /**
     * Constructor.
     */
    public function __construct() {
        $this->authtype = 'beaconlms';
        $this->config = get_config('auth_beaconlms');
    }

    /**
     * Old syntax of class constructor. Deprecated in PHP7.
     *
     * @deprecated since Moodle 3.1
     */
    public function auth_plugin_beaconlms() {
        debugging('Use of class name as constructor is deprecated', DEBUG_DEVELOPER);
        self::__construct();
    }

    /**
     * Returns true if the username and password work and false if they are
     * wrong or don't exist.
     *
     * @param string $username The username
     * @param string $password The password
     * @return bool Authentication success or failure.
     */
    public function user_login($username, $password) {
        // This plugin doesn't use traditional username/password authentication
        return false;
    }

    /**
     * Updates the user's password.
     *
     * @param  object  $user        User table object
     * @param  string  $newpassword Plaintext password
     * @return boolean result
     */
    public function user_update_password($user, $newpassword) {
        // Password updates are handled by BeaconLMS
        return false;
    }

    /**
     * Returns true if this authentication plugin is 'internal'.
     *
     * @return bool
     */
    public function is_internal() {
        return false;
    }

    /**
     * Returns true if this authentication plugin can change the user's
     * password.
     *
     * @return bool
     */
    public function can_change_password() {
        return false;
    }

    /**
     * Returns the URL for changing the user's pw, or empty if the default can
     * be used.
     *
     * @return moodle_url
     */
    public function change_password_url() {
        return null;
    }

    /**
     * Returns true if plugin allows resetting of internal password.
     *
     * @return bool
     */
    public function can_reset_password() {
        return false;
    }

    /**
     * Returns true if plugin can be manually set.
     *
     * @return bool
     */
    public function can_be_manually_set() {
        return false;
    }

    /**
     * Validate the SSO token from BeaconLMS
     *
     * @param string $username
     * @param int $timestamp
     * @param string $nonce
     * @param string $token
     * @param int $beacon_user_id
     * @return bool
     */
    public function validate_sso_token($username, $timestamp, $nonce, $token, $beacon_user_id) {
        global $CFG;

        // Check if token has expired
        $expiry_minutes = $this->config->token_expiry_minutes ?: 5;
        $expiry_time = $expiry_minutes * 60; // Convert to seconds
        
        if (time() - $timestamp > $expiry_time) {
            error_log('BeaconLMS SSO: Token expired');
            return false;
        }

        // Generate expected token using the same algorithm as BeaconLMS
        $shared_secret = $this->config->shared_secret ?: '';
        if (empty($shared_secret)) {
            error_log('BeaconLMS SSO: No shared secret configured');
            return false;
        }

        $data = $username . '|' . $timestamp . '|' . $nonce . '|' . $beacon_user_id . '|' . $shared_secret;
        $expected_token = hash('sha256', $data);

        if (!hash_equals($expected_token, $token)) {
            error_log('BeaconLMS SSO: Invalid token');
            return false;
        }

        return true;
    }

    /**
     * Get user information from BeaconLMS API (optional enhancement)
     *
     * @param int $beacon_user_id
     * @return array|false
     */
    private function get_beacon_user_info($beacon_user_id) {
        // This could be enhanced to fetch user info from BeaconLMS API
        // For now, we rely on the user already existing in Moodle
        return false;
    }
}
