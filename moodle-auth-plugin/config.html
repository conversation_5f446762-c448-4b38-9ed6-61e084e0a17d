<?php
// This file is part of Moodle - http://moodle.org/
//
// Moodle is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.

/**
 * BeaconLMS SSO Authentication Plugin Configuration
 *
 * @package    auth_beaconlms
 * @copyright  2025 BeaconLMS
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

if (!defined('MOODLE_INTERNAL')) {
    die('Direct access to this script is forbidden.');    ///  It must be included from a Moodle page
}

$settings->add(new admin_setting_heading('auth_beaconlms/pluginname', 
    new lang_string('pluginname', 'auth_beaconlms'), 
    new lang_string('auth_beaconlmsdescription', 'auth_beaconlms')));

$settings->add(new admin_setting_configtext('auth_beaconlms/shared_secret',
    get_string('shared_secret', 'auth_beaconlms'),
    get_string('shared_secret_desc', 'auth_beaconlms'), '', PARAM_TEXT));

$settings->add(new admin_setting_configtext('auth_beaconlms/token_expiry_minutes',
    get_string('token_expiry_minutes', 'auth_beaconlms'),
    get_string('token_expiry_minutes_desc', 'auth_beaconlms'), '5', PARAM_INT));

?>

<table cellspacing="0" cellpadding="5" border="0">
<tr>
   <td colspan="3">
        <h2 class="main"><?php print_string('auth_beaconlms_settings', 'auth_beaconlms') ?></h2>
   </td>
</tr>
<tr>
    <td align="right"><label for="shared_secret"><?php print_string('shared_secret', 'auth_beaconlms') ?></label></td>
    <td><input id="shared_secret" name="shared_secret" type="text" size="30" value="<?php echo $config->shared_secret ?? '' ?>" /></td>
    <td><?php print_string('shared_secret_desc', 'auth_beaconlms') ?></td>
</tr>
<tr>
    <td align="right"><label for="token_expiry_minutes"><?php print_string('token_expiry_minutes', 'auth_beaconlms') ?></label></td>
    <td><input id="token_expiry_minutes" name="token_expiry_minutes" type="text" size="5" value="<?php echo $config->token_expiry_minutes ?? '5' ?>" /></td>
    <td><?php print_string('token_expiry_minutes_desc', 'auth_beaconlms') ?></td>
</tr>
<tr>
    <td colspan="3">
        <h4><?php print_string('sso_instructions', 'auth_beaconlms') ?></h4>
        <p><?php print_string('sso_instructions_desc', 'auth_beaconlms') ?></p>
        <p><strong><?php print_string('sso_url', 'auth_beaconlms') ?>:</strong> 
        <code><?php echo $CFG->wwwroot ?>/auth/beaconlms/sso.php</code></p>
    </td>
</tr>
</table>
