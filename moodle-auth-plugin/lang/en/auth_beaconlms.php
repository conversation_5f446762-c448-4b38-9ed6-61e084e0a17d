<?php
// This file is part of Moodle - http://moodle.org/
//
// Moodle is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.

/**
 * Language strings for BeaconLMS SSO Authentication Plugin
 *
 * @package    auth_beaconlms
 * @copyright  2025 BeaconLMS
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

$string['pluginname'] = 'BeaconLMS SSO';
$string['auth_beaconlmsdescription'] = 'Single Sign-On authentication plugin for BeaconLMS integration. Allows users to seamlessly log into Moodle from BeaconLMS using secure token-based authentication.';
$string['auth_beaconlms_settings'] = 'BeaconLMS SSO Settings';
$string['shared_secret'] = 'Shared Secret';
$string['shared_secret_desc'] = 'Secret key shared between BeaconLMS and Moodle for secure token generation. This must match the secret configured in BeaconLMS.';
$string['token_expiry_minutes'] = 'Token Expiry (Minutes)';
$string['token_expiry_minutes_desc'] = 'Number of minutes before SSO tokens expire. Default is 5 minutes for security.';
$string['sso_instructions'] = 'SSO Setup Instructions';
$string['sso_instructions_desc'] = 'To complete the SSO setup, configure BeaconLMS to use the following SSO endpoint URL:';
$string['sso_url'] = 'SSO Endpoint URL';
$string['invalidtoken'] = 'Invalid or expired SSO token. Please try logging in again.';
$string['usernotfound'] = 'User not found in Moodle. Please contact your administrator.';
$string['usersuspended'] = 'Your account has been suspended. Please contact your administrator.';
$string['privacy:metadata'] = 'The BeaconLMS SSO authentication plugin does not store any personal data.';
