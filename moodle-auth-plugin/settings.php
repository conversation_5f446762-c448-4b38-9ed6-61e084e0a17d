<?php
// This file is part of Moodle - http://moodle.org/
//
// Moodle is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.

/**
 * Settings for the BeaconLMS SSO authentication plugin.
 *
 * @package    auth_beaconlms
 * @copyright  2025 BeaconLMS
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die;

if ($ADMIN->fulltree) {
    // --- settings heading ---
    $settings->add(new admin_setting_heading('auth_beaconlms/pluginname',
        get_string('pluginname', 'auth_beaconlms'),
        get_string('auth_beaconlmsdescription', 'auth_beaconlms')
    ));

    // --- shared secret ---
    $settings->add(new admin_setting_configpasswordunmask('auth_beaconlms/shared_secret',
        get_string('shared_secret', 'auth_beaconlms'),
        get_string('shared_secret_desc', 'auth_beaconlms'),
        '',
        PARAM_TEXT
    ));

    // --- token expiry ---
    $settings->add(new admin_setting_configtext('auth_beaconlms/token_expiry_minutes',
        get_string('token_expiry_minutes', 'auth_beaconlms'),
        get_string('token_expiry_minutes_desc', 'auth_beaconlms'),
        5,
        PARAM_INT
    ));
}
