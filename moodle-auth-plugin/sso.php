<?php
// This file is part of Moodle - http://moodle.org/
//
// Moodle is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.

/**
 * BeaconLMS SSO Entry Point
 *
 * This script handles SSO login requests from BeaconLMS
 *
 * @package    auth_beaconlms
 * @copyright  2025 BeaconLMS
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

require_once('../../config.php');
require_once($CFG->dirroot.'/auth/beaconlms/auth.php');

// Get parameters from the SSO request
$username = required_param('username', PARAM_EMAIL);
$timestamp = required_param('timestamp', PARAM_INT);
$nonce = required_param('nonce', PARAM_ALPHANUMEXT);
$token = required_param('token', PARAM_ALPHANUMEXT);
$beacon_user_id = required_param('beacon_user_id', PARAM_INT);

// Initialize the authentication plugin
$auth = new auth_plugin_beaconlms();

// Validate the SSO token
if (!$auth->validate_sso_token($username, $timestamp, $nonce, $token, $beacon_user_id)) {
    // Log the failed attempt
    error_log("BeaconLMS SSO: Authentication failed for user $username (Beacon ID: $beacon_user_id)");
    
    // Redirect to login page with error
    $loginurl = new moodle_url('/login/index.php', array('errorcode' => 'invalidtoken'));
    redirect($loginurl);
    exit;
}

// Find the user in Moodle
$user = $DB->get_record('user', array('username' => $username, 'deleted' => 0));

if (!$user) {
    // User not found - this shouldn't happen if BeaconLMS properly syncs users
    error_log("BeaconLMS SSO: User not found in Moodle: $username");
    
    $loginurl = new moodle_url('/login/index.php', array('errorcode' => 'usernotfound'));
    redirect($loginurl);
    exit;
}

// Check if user is suspended
if ($user->suspended) {
    error_log("BeaconLMS SSO: Suspended user attempted login: $username");
    
    $loginurl = new moodle_url('/login/index.php', array('errorcode' => 'usersuspended'));
    redirect($loginurl);
    exit;
}

// Successful authentication - log the user in
complete_user_login($user);

// Log successful SSO login
error_log("BeaconLMS SSO: Successful login for user $username (Beacon ID: $beacon_user_id)");

// Add a custom event for tracking SSO logins
$event = \core\event\user_loggedin::create(array(
    'userid' => $user->id,
    'objectid' => $user->id,
    'other' => array(
        'username' => $user->username,
        'sso_source' => 'beaconlms',
        'beacon_user_id' => $beacon_user_id
    )
));
$event->trigger();

// Redirect to the main course or dashboard
$redirect_url = new moodle_url('/my/');

// Check if there's a specific course to redirect to
if (!empty($CFG->defaulthomepage) && $CFG->defaulthomepage == HOMEPAGE_SITE) {
    $redirect_url = new moodle_url('/');
} else if (!empty($CFG->defaulthomepage) && $CFG->defaulthomepage == HOMEPAGE_MYCOURSES) {
    $redirect_url = new moodle_url('/my/courses.php');
}

// You can also redirect to a specific course if needed
// $course_id = get_config('auth_beaconlms', 'default_course_id');
// if ($course_id) {
//     $redirect_url = new moodle_url('/course/view.php', array('id' => $course_id));
// }

redirect($redirect_url);
