/**
 * BeaconLMS Student Dashboard JavaScript
 * Handles sync functionality and notifications
 */

window.BeaconLMS = window.BeaconLMS || {};

/**
 * Sync progress with Moodle
 */
BeaconLMS.syncProgress = function() {
    const button = document.getElementById('syncButton');
    const text = document.getElementById('syncText');

    if (!button || !text) {
        console.error('Sync button elements not found');
        return;
    }

    // Disable button and show loading state
    button.disabled = true;
    text.innerHTML = '⏳ Syncing...';

    // Get CSRF token
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
    if (!csrfToken) {
        console.error('CSRF token not found');
        BeaconLMS.showNotification('❌ Security token missing. Please refresh the page.', 'error');
        return;
    }

    // Send AJAX request to sync progress endpoint
    fetch('/student/sync-progress', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': csrfToken,
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        if (response.ok) {
            return response.json();
        }
        throw new Error(`HTTP error! status: ${response.status}`);
    })
    .then(data => {
        if (data.success) {
            // Show success message
            BeaconLMS.showNotification('✅ ' + data.message, 'success');

            // Update sync status (refresh page after 2 seconds)
            setTimeout(() => {
                location.reload();
            }, 2000);
        } else {
            // Show error message
            BeaconLMS.showNotification('❌ ' + (data.message || 'Sync failed. Please try again.'), 'error');
        }
    })
    .catch(error => {
        console.error('Sync error:', error);
        BeaconLMS.showNotification('❌ Sync failed. Please try again.', 'error');
    })
    .finally(() => {
        // Re-enable button after 5 seconds
        setTimeout(() => {
            if (button && text) {
                button.disabled = false;
                text.innerHTML = '🔄 Sync Progress';
            }
        }, 5000);
    });
};

/**
 * Show notification to user
 */
BeaconLMS.showNotification = function(message, type) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full ${
        type === 'success' ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
    }`;

    notification.innerHTML = `
        <div class="flex">
            <div class="ml-3">
                <p class="text-sm font-medium ${type === 'success' ? 'text-green-800' : 'text-red-800'}">${message}</p>
            </div>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-gray-400 hover:text-gray-600">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // Auto remove after 5 seconds
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 300);
    }, 5000);
};

/**
 * Initialize dashboard functionality
 */
BeaconLMS.initDashboard = function() {
    console.log('BeaconLMS Dashboard initialized');
};

// Auto-initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    BeaconLMS.initDashboard();
});
