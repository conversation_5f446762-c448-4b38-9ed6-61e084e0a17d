@extends('layouts.app')

@section('content')
<div class="min-h-screen bg-gray-f8 flex flex-col justify-center py-12 px-6 lg:px-8 font-gotham-narrow">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
        <!-- Logo and Title -->
        <div class="text-center space-y-4">
            <div class="flex items-center justify-center space-x-3">
                <img
                    src="https://api.builder.io/api/v1/image/assets/TEMP/73eabfc9a05bd5a1edc4faf38fb9480483c7cc05?width=252"
                    alt="Beacon FinTrain Logo"
                    class="h-[30px] object-contain"
                />

            </div>
            <h2 class="text-dark-blue text-2xl font-[350] tracking-[-0.2px]">Welcome Back</h2>
            <p class="text-dark-blue text-[14px] font-[325] tracking-[-0.14px]">
                Sign in to continue your learning journey
            </p>
        </div>
    </div>

    <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <!-- Login Form Card -->
        <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
            <form class="space-y-6" action="{{ route('login') }}" method="POST">
                @csrf

                <!-- Email Field -->
                <div class="space-y-2">
                    <label for="email" class="block text-dark-blue text-[14px] font-[350] tracking-[-0.14px]">
                        Email address
                    </label>
                    <div class="mt-1">
                        <input
                            id="email"
                            name="email"
                            type="email"
                            autocomplete="email"
                            required
                            value="{{ old('email') }}"
                            class="block w-full rounded-lg border border-gray-300 px-4 py-3 text-dark-blue bg-white placeholder:text-gray-b2 focus:border-light-blue focus:ring-2 focus:ring-light-blue/20 focus:outline-none transition-colors @error('email') border-red-500 @enderror"
                            placeholder="Enter your email address"
                        >
                    </div>
                    @error('email')
                        <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Password Field -->
                <div class="space-y-2">
                    <div class="flex items-center justify-between">
                        <label for="password" class="block text-dark-blue text-[14px] font-[350] tracking-[-0.14px]">
                            Password
                        </label>
                        @if (Route::has('password.request'))
                            <div class="text-sm">
                                <a href="{{ route('password.request') }}" class="font-[350] text-light-blue hover:text-light-blue/80 transition-colors">
                                    Forgot password?
                                </a>
                            </div>
                        @endif
                    </div>
                    <div class="mt-1">
                        <input
                            id="password"
                            name="password"
                            type="password"
                            autocomplete="current-password"
                            required
                            class="block w-full rounded-lg border border-gray-300 px-4 py-3 text-dark-blue bg-white placeholder:text-gray-b2 focus:border-light-blue focus:ring-2 focus:ring-light-blue/20 focus:outline-none transition-colors @error('password') border-red-500 @enderror"
                            placeholder="Enter your password"
                        >
                    </div>
                    @error('password')
                        <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Remember Me -->
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <input
                            id="remember"
                            name="remember"
                            type="checkbox"
                            {{ old('remember') ? 'checked' : '' }}
                            class="h-4 w-4 rounded border-gray-300 text-light-blue focus:ring-light-blue focus:ring-2"
                        >
                        <label for="remember" class="ml-3 block text-dark-blue text-[14px] font-[325]">
                            Remember me
                        </label>
                    </div>
                </div>

                <!-- Sign In Button -->
                <div>
                    <button
                        type="submit"
                        class="flex w-full justify-center rounded-lg bg-yellow px-4 py-3 text-dark-blue font-[350] text-[14px] tracking-[-0.14px] shadow-sm hover:bg-yellow/90 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-yellow transition-colors"
                    >
                        Sign In
                    </button>
                </div>
            </form>

            <!-- Register Link -->
            <div class="mt-8 text-center">
                <p class="text-dark-blue text-[14px] font-[325]">
                    Don't have an account?
                    <a href="{{ route('register') }}" class="font-[350] text-light-blue hover:text-light-blue/80 transition-colors">
                        Create one here
                    </a>
                </p>
            </div>
        </div>

        <!-- Additional Info -->
        <div class="mt-6 text-center">
            <div class="bg-gray-f8 rounded-lg p-4">
                <p class="text-dark-blue text-[12px] font-[325] tracking-[-0.12px]">
                    Access your personalized learning dashboard and continue your CTP certification journey
                </p>
            </div>
        </div>
    </div>
</div>
@endsection
