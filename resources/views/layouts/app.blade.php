<!doctype html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'BeaconLMS') }}</title>

    <!-- Fonts -->
    <link rel="dns-prefetch" href="//fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=Nunito" rel="stylesheet">

    <!-- Gotham Narrow Alternative - Using system fonts with similar characteristics -->
    <style>
        @font-face {
            font-family: 'Gotham Narrow';
            src: local('Arial Narrow'), local('Helvetica Neue Condensed'), local('Roboto Condensed');
            font-weight: 325;
            font-style: normal;
        }
        @font-face {
            font-family: 'Gotham Narrow';
            src: local('Arial Narrow'), local('Helvetica Neue Condensed'), local('Roboto Condensed');
            font-weight: 350;
            font-style: normal;
        }
        @font-face {
            font-family: 'Gotham Narrow';
            src: local('Arial Narrow'), local('Helvetica Neue Condensed'), local('Roboto Condensed');
            font-weight: 400;
            font-style: normal;
        }
    </style>

    <!-- Tailwind CSS - Direct CDN Loading -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        sans: ['Figtree', 'ui-sans-serif', 'system-ui', 'sans-serif'],
                        'gotham': ['Gotham Narrow', 'Arial Narrow', 'Helvetica Neue Condensed', 'Roboto Condensed', 'sans-serif'],
                        'gotham-narrow': ['Gotham Narrow', 'Arial Narrow', 'Helvetica Neue Condensed', 'Roboto Condensed', 'sans-serif'],
                        'inter': ['Inter', '-apple-system', 'Roboto', 'Helvetica', 'sans-serif'],
                        'poppins': ['Poppins', '-apple-system', 'Roboto', 'Helvetica', 'sans-serif'],
                    },
                    fontWeight: {
                        '325': '325',
                        '350': '350',
                    },
                    colors: {
                        'ctp-blue': '#1e40af',
                        'ctp-purple': '#7c3aed',
                        'dark-blue': '#102649',
                        'light-blue': '#007DB6',
                        'yellow': '#F7C100',
                        'gray-f8': '#F8F9FA',
                        'gray-ee': '#EEE',
                        'gray-b2': '#B2B2B2',
                        'orange-red': '#E54B20',
                        'gray-5': 'rgba(16, 38, 73, 0.05)',
                        'black-40': 'rgba(0, 0, 0, 0.40)',
                    }
                }
            }
        }
    </script>

    <!-- Scripts -->
    @vite(['resources/js/app.js'])
</head>
<body>
    <div id="app">
        <nav class="navbar navbar-expand-md navbar-light bg-white shadow-sm">
            <div class="container">
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="{{ __('Toggle navigation') }}">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarSupportedContent">
                    <!-- Left Side Of Navbar -->
                    <ul class="navbar-nav me-auto">

                    </ul>

                    <!-- Right Side Of Navbar -->
                    <ul class="navbar-nav ms-auto">
                        <!-- Authentication Links -->
                        @guest
                            @if (Route::has('login'))
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ route('login') }}">{{ __('Login') }}</a>
                                </li>
                            @endif

                            @if (Route::has('register'))
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ route('register') }}">{{ __('Register') }}</a>
                                </li>
                            @endif
                        @else
                            <li class="nav-item dropdown">
                                <a id="navbarDropdown" class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false" v-pre>
                                    {{ Auth::user()->name }}
                                </a>

                                <div class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                                    <a class="dropdown-item" href="{{ route('logout') }}"
                                       onclick="event.preventDefault();
                                                     document.getElementById('logout-form').submit();">
                                        {{ __('Logout') }}
                                    </a>

                                    <form id="logout-form" action="{{ route('logout') }}" method="POST" class="d-none">
                                        @csrf
                                    </form>
                                </div>
                            </li>
                        @endguest
                    </ul>
                </div>
            </div>
        </nav>

        <main class="py-4">
            @yield('content')
        </main>
    </div>
</body>
</html>
