<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="h-full bg-white">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title', 'BeaconLMS - Student Dashboard')</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- <PERSON> Narrow Alternative - Using system fonts with similar characteristics -->
    <style>
        @font-face {
            font-family: 'Gotham Narrow';
            src: local('Arial Narrow'), local('Helvetica Neue Condensed'), local('Roboto Condensed');
            font-weight: 325;
            font-style: normal;
        }
        @font-face {
            font-family: 'Gotham Narrow';
            src: local('Arial Narrow'), local('Helvetica Neue Condensed'), local('Roboto Condensed');
            font-weight: 350;
            font-style: normal;
        }
        @font-face {
            font-family: 'Gotham Narrow';
            src: local('Arial Narrow'), local('Helvetica Neue Condensed'), local('Roboto Condensed');
            font-weight: 400;
            font-style: normal;
        }
    </style>

    <!-- Tailwind CSS - Direct CDN Loading -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        sans: ['Figtree', 'ui-sans-serif', 'system-ui', 'sans-serif'],
                        'gotham': ['Gotham Narrow', 'Arial Narrow', 'Helvetica Neue Condensed', 'Roboto Condensed', 'sans-serif'],
                        'gotham-narrow': ['Gotham Narrow', 'Arial Narrow', 'Helvetica Neue Condensed', 'Roboto Condensed', 'sans-serif'],
                        'inter': ['Inter', '-apple-system', 'Roboto', 'Helvetica', 'sans-serif'],
                        'poppins': ['Poppins', '-apple-system', 'Roboto', 'Helvetica', 'sans-serif'],
                    },
                    fontWeight: {
                        '325': '325',
                        '350': '350',
                    },
                    colors: {
                        'ctp-blue': '#1e40af',
                        'ctp-purple': '#7c3aed',
                        'dark-blue': '#102649',
                        'light-blue': '#007DB6',
                        'yellow': '#F7C100',
                        'gray-f8': '#F8F9FA',
                        'gray-ee': '#EEE',
                        'gray-b2': '#B2B2B2',
                        'orange-red': '#E54B20',
                        'gray-5': 'rgba(16, 38, 73, 0.05)',
                        'black-40': 'rgba(0, 0, 0, 0.40)',
                    }
                }
            }
        }
    </script>

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    @livewireStyles
</head>
<body class="min-h-screen bg-white flex flex-col font-gotham">
<!-- Main Container -->
<div class="min-h-screen flex flex-col">
    <!-- Header -->
    <header class="w-full h-[60px] bg-white border-b border-gray-200 flex items-center justify-between px-6">
        <!-- Logo -->
        <div class="flex items-center">
            <div class="flex items-center justify-center h-[40px]">
                <img
                    src="https://api.builder.io/api/v1/image/assets/TEMP/73eabfc9a05bd5a1edc4faf38fb9480483c7cc05?width=252"
                    alt="Beacon FinTrain Logo"
                    class="h-[30px] object-contain"
                />
            </div>
        </div>

        <!-- Navigation and User Menu -->
        <div class="flex items-center justify-between">
            <!-- Navigation Links -->
            <div class="flex items-center gap-6 font-gotham text-dark-blue">
                <a href="#" class="font-gotham-narrow text-[16px] font-[350] hover:text-light-blue transition-colors">
                    Learner
                </a>
                <a href="#" class="font-gotham-narrow text-[16px] font-[350] hover:text-light-blue transition-colors">
                    Member Dashboard
                </a>
            </div>

            <!-- Right Side - Theme, Notifications, Account -->
            <div class="flex items-center gap-4 ml-8">
                <!-- Dark Mode Toggle -->
                <button
                    class="w-6 h-6 flex items-center justify-center hover:bg-gray-100 rounded-full transition-colors">
                    <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                    </svg>
                </button>

                <!-- Notifications -->
                <div class="relative">
                    <button class="hover:bg-gray-100 p-1 rounded-full transition-colors">
                        <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M15 17h5l-5 5v-5zM4 19h10a2 2 0 002-2V7a2 2 0 00-2-2H4a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                    </button>
                    <div class="absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full"></div>
                </div>

                <!-- Account Dropdown -->
                <div x-data="{ open: false }" class="relative">
                    <button @click="open = !open"
                            class="flex items-center gap-1 hover:bg-gray-100 px-2 py-1 rounded transition-colors">
                            <span class="font-gotham-narrow text-[16px] font-[350] text-dark-blue">
                                My Account
                            </span>
                        <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div x-show="open" @click.away="open = false"
                         class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10"
                         style="display: none;">
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Your Profile</a>
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Settings</a>
                        <form method="POST" action="/logout">
                            @csrf
                            <button type="submit"
                                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                Sign out
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Layout -->
    <div class="flex flex-1">
        <!-- Sidebar -->
        <div class="w-[254px] h-full bg-gray-f8 flex flex-col">
            <div class="flex-1 p-[11px]">
                <div class="space-y-1">
                    <!-- My Dashboard - Active -->
                    <a href="#" class="flex items-center gap-3 px-6 py-3 bg-dark-blue rounded text-gray-ee">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                        </svg>
                        <span class="font-gotham-narrow text-[14px] font-[350] leading-normal tracking-[-0.14px]">
                                My Dashboard
                            </span>
                    </a>

                    <!-- My Learning Journey - Expandable -->
                    <div class="space-y-1">
                        <div
                            class="flex items-center justify-between px-6 py-3 text-dark-blue hover:bg-white/50 rounded cursor-pointer">
                            <div class="flex items-center gap-3">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                </svg>
                                <span class="font-gotham-narrow text-[14px] font-[350] leading-normal tracking-[-0.14px]">
                                        My Learning Journey
                                    </span>
                            </div>
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </div>

                        <!-- Sub-items -->
                        <div class="pl-[60px] space-y-1">
                            <div
                                class="py-2 text-dark-blue font-gotham-narrow text-[14px] font-[350] leading-normal tracking-[-0.14px] cursor-pointer hover:bg-white/50 rounded px-4">
                                In-progress Study Plan
                            </div>
                            <div
                                class="py-2 text-dark-blue font-gotham-narrow text-[14px] font-[350] leading-normal tracking-[-0.14px] cursor-pointer hover:bg-white/50 rounded px-4">
                                Bookmarks & Notes
                            </div>
                            <div
                                class="py-2 text-dark-blue font-gotham-narrow text-[14px] font-[350] leading-normal tracking-[-0.14px] cursor-pointer hover:bg-white/50 rounded px-4">
                                Results & Certificates
                            </div>
                        </div>
                    </div>

                    <!-- Get Certified -->
                    <a href="#"
                       class="flex items-center gap-3 px-6 py-3 text-dark-blue hover:bg-white/50 rounded cursor-pointer">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                        </svg>
                        <span class="font-gotham-narrow text-[14px] font-[350] leading-normal tracking-[-0.14px]">
                                Get Certified
                            </span>
                    </a>

                    <!-- Resource Center - Expandable -->
                    <div class="space-y-1">
                        <div
                            class="flex items-center justify-between px-6 py-3 text-dark-blue hover:bg-white/50 rounded cursor-pointer">
                            <div class="flex items-center gap-3">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0H8m8 0v2a2 2 0 01-2 2H10a2 2 0 01-2-2V6"></path>
                                </svg>
                                <span class="font-gotham-narrow text-[14px] font-[350] leading-normal tracking-[-0.14px]">
                                        Resource Center
                                    </span>
                            </div>
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </div>

                        <!-- Sub-items -->
                        <div class="pl-[60px] space-y-1">
                            <div
                                class="py-2 text-dark-blue font-gotham-narrow text-[14px] font-[350] leading-normal tracking-[-0.14px] cursor-pointer hover:bg-white/50 rounded px-4">
                                Skills Assessments
                            </div>
                            <div
                                class="py-2 text-dark-blue font-gotham-narrow text-[14px] font-[350] leading-normal tracking-[-0.14px] cursor-pointer hover:bg-white/50 rounded px-4">
                                Case Study Challenges
                            </div>
                            <div
                                class="py-2 text-dark-blue font-gotham-narrow text-[14px] font-[350] leading-normal tracking-[-0.14px] cursor-pointer hover:bg-white/50 rounded px-4">
                                Games & Activities
                            </div>
                        </div>
                    </div>

                    <!-- Community -->
                    <a href="#"
                       class="flex items-center gap-3 px-6 py-3 text-dark-blue hover:bg-white/50 rounded cursor-pointer">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                        </svg>
                        <span class="font-gotham-narrow text-[14px] font-[350] leading-normal tracking-[-0.14px]">
                                Community
                            </span>
                    </a>

                    <!-- Ask The Expert -->
                    <a href="#"
                       class="flex items-center gap-3 px-6 py-3 text-dark-blue hover:bg-white/50 rounded cursor-pointer">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
                        </svg>
                        <span class="font-gotham-narrow text-[16px] font-[350] leading-normal tracking-[-0.16px]">
                                Ask The Expert
                            </span>
                    </a>
                </div>
            </div>

            <!-- Bottom Section -->
            <div class="p-[11px] space-y-1">
                <!-- Settings -->
                <a href="#"
                   class="flex items-center gap-3 px-6 py-3 text-dark-blue hover:bg-white/50 rounded cursor-pointer">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    <span class="font-gotham-narrow text-[16px] font-[350] leading-normal tracking-[-0.16px]">
                            Settings
                        </span>
                </a>

                <!-- Log out -->
                <form method="POST" action="/logout">
                    @csrf
                    <button type="submit"
                            class="w-full flex items-center gap-3 px-6 py-3 bg-white/20 rounded cursor-pointer">
                        <svg class="w-6 h-6 text-orange-red" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                        </svg>
                        <span
                            class="font-poppins text-[14px] font-medium leading-normal tracking-[-0.14px] text-orange-red text-left">
                                Log out
                            </span>
                    </button>
                </form>
            </div>
        </div>

        <!-- Content Area -->
        <div class="flex-1 flex">
            <!-- Main Content -->
            <div class="flex-1 p-6">
                @yield('content')
            </div>
        </div>
    </div>
</div>

@livewireScripts

@stack('scripts')
</body>
</html>
