<div class="space-y-2 font-gotham-narrow">
    @if($chapters && $chapters->count() > 0)
        @foreach($chapters as $chapter)
            <div class="bg-gray-f8 rounded-xl p-4">
                <!-- Chapter Header -->
                <div class="cursor-pointer" wire:click="toggleChapter({{ $chapter->id }})">
                    <div class="flex items-start gap-2">
                        <div
                            class="w-6 h-6 bg-dark-blue rounded-full flex items-center justify-center text-gray-ee font-gotham-narrow text-[15px] font-[400]">
                            {{ $chapter->order }}
                        </div>
                        <div class="flex-1 space-y-3">
                            <div class="flex items-center justify-between">
                                <span
                                    class="text-dark-blue font-gotham-narrow text-[15px] font-[350] tracking-[-0.15px]">
                                    CHAPTER {{ $chapter->order }}: {{ $chapter->title }}
                                </span>
                                <svg
                                    class="w-6 h-6 text-dark-blue transform transition-transform {{ $expandedChapter == $chapter->id ? 'rotate-180' : '' }}"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="{{ $expandedChapter == $chapter->id ? 'M5 15l7-7 7 7' : 'M19 9l-7 7-7-7' }}"></path>
                                </svg>
                            </div>

                            <!-- Progress Bar -->
                            <div class="flex items-center gap-4">
                                <div class="flex-1 h-[5px] bg-white rounded-full relative">
                                    <div class="h-[5px] bg-yellow rounded-full" style="width: {{ $chapter->progress }}%"></div>
                                </div>
                                <span class="text-gray-b2 font-gotham-narrow text-[10px] font-[350]">{{ $chapter->progress }}%</span>
                            </div>

                            <p class="text-dark-blue font-gotham-narrow text-[12px] font-[325] leading-4 tracking-[-0.12px]">
                                {{ strip_tags($chapter->description) ?? 'This chapter discusses the basic role and objectives of treasury management, the potential organizational structures for treasury, and the relationship between treasury management and corporate financial management.' }}
                            </p>

                            @if($chapter->order == 1)
                                <!-- Skip Test Card -->
                                <div class="border border-dark-blue/20 bg-white rounded p-4 space-y-3">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center gap-3">
                                            <svg class="w-4 h-4 text-dark-blue" fill="none" stroke="currentColor"
                                                 viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                      d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                            </svg>
                                            <span class="text-dark-blue font-gotham-narrow text-[12px] font-[350]">
                                                Already know this chapter ?
                                            </span>
                                        </div>
                                        <button wire:click.stop="takeTest({{ $chapter->id }})"
                                                class="bg-dark-blue text-gray-ee rounded px-4 py-2 font-gotham-narrow text-[12px] font-[350] hover:bg-dark-blue/80 transition-colors">
                                            Take the test
                                        </button>
                                    </div>
                                    <p class="text-dark-blue font-gotham-narrow text-[12px] font-[325] leading-4 tracking-[-0.12px]">
                                        Take a quick test to check if you can skip the chapter.
                                    </p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Chapter Content (Expandable) -->
                @if($expandedChapter == $chapter->id)
                    <!-- Chapter Content List -->
                    <div class="bg-white rounded p-4 space-y-4 mt-3">
                        <div class="flex items-center justify-between">
                            <div
                                class="text-light-blue font-gotham-narrow text-[12px] font-[350] underline cursor-pointer hover:text-light-blue/80">
                                hide chapter details
                            </div>
                            <button wire:click="startChapter({{ $chapter->id }})"
                                    class="bg-yellow text-dark-blue rounded px-4 py-2 font-gotham-narrow text-[12px] font-[350] hover:bg-yellow/80 transition-colors">
                                Start Chapter
                            </button>
                        </div>

                        <!-- Chapter Items -->
                        <div class="space-y-3">
                            @if($chapter->items && $chapter->items->count() > 0)
                                @foreach($chapter->items as $item)
                                    <div class="flex items-center justify-between py-2">
                                        <div class="flex items-center space-x-3">
                                            @if($item->type == 'video')
                                                <svg class="w-4 h-4 text-dark-blue" fill="none" stroke="currentColor"
                                                     viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                          stroke-width="2"
                                                          d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9 5a9 9 0 1118 0H3z"></path>
                                                </svg>
                                            @elseif($item->type == 'quiz')
                                                <svg class="w-4 h-4 text-dark-blue" fill="none" stroke="currentColor"
                                                     viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                          stroke-width="2"
                                                          d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                                </svg>
                                            @else
                                                <svg class="w-4 h-4 text-dark-blue" fill="none" stroke="currentColor"
                                                     viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                          stroke-width="2"
                                                          d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9 5a9 9 0 1118 0H3z"></path>
                                                </svg>
                                            @endif
                                            <span
                                                class="text-dark-blue font-gotham-narrow text-[12px] font-[350]">{{ $item->title }}</span>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <span class="text-dark-blue font-gotham-narrow text-[12px] font-[350]">{{ $item->xp_points }} XP</span>
                                            @if($item->is_completed)
                                                <svg class="w-4 h-4 text-green-500" fill="currentColor"
                                                     viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd"
                                                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                                          clip-rule="evenodd"></path>
                                                </svg>
                                            @endif
                                        </div>
                                    </div>
                                @endforeach
                            @else
                                <!-- Default items for demo -->
                                <div class="flex items-center justify-between py-2">
                                    <div class="flex items-center space-x-3">
                                        <svg class="w-4 h-4 text-dark-blue" fill="none" stroke="currentColor"
                                             viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                  d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9 5a9 9 0 1118 0H3z"></path>
                                        </svg>
                                        <span class="text-dark-blue font-gotham-narrow text-[12px] font-[350]">Introduction Video</span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <span
                                            class="text-dark-blue font-gotham-narrow text-[12px] font-[350]">50 XP</span>
                                        <svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd"
                                                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                                  clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between py-2">
                                    <div class="flex items-center space-x-3">
                                        <svg class="w-4 h-4 text-dark-blue" fill="none" stroke="currentColor"
                                             viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                  d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9 5a9 9 0 1118 0H3z"></path>
                                        </svg>
                                        <span class="text-dark-blue font-gotham-narrow text-[12px] font-[350]">What is financial statement?</span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <span
                                            class="text-dark-blue font-gotham-narrow text-[12px] font-[350]">50 XP</span>
                                        <svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd"
                                                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                                  clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between py-2">
                                    <div class="flex items-center space-x-3">
                                        <svg class="w-4 h-4 text-dark-blue" fill="none" stroke="currentColor"
                                             viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                  d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                        </svg>
                                        <span
                                            class="text-dark-blue font-gotham-narrow text-[12px] font-[350]">Quiz 01</span>
                                    </div>
                                    <span class="text-dark-blue font-gotham-narrow text-[12px] font-[350]">80 XP</span>
                                </div>
                                <div class="flex items-center justify-between py-2">
                                    <div class="flex items-center space-x-3">
                                        <svg class="w-4 h-4 text-dark-blue" fill="none" stroke="currentColor"
                                             viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                  d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9 5a9 9 0 1118 0H3z"></path>
                                        </svg>
                                        <span class="text-dark-blue font-gotham-narrow text-[12px] font-[350]">Why learning financial statement?</span>
                                    </div>
                                    <span class="text-dark-blue font-gotham-narrow text-[12px] font-[350]">50 XP</span>
                                </div>
                                <div class="flex items-center justify-between py-2">
                                    <div class="flex items-center space-x-3">
                                        <svg class="w-4 h-4 text-dark-blue" fill="none" stroke="currentColor"
                                             viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                  d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9 5a9 9 0 1118 0H3z"></path>
                                        </svg>
                                        <span
                                            class="text-dark-blue font-gotham-narrow text-[12px] font-[350]">Chapter Summary</span>
                                    </div>
                                    <span class="text-dark-blue font-gotham-narrow text-[12px] font-[350]">70 XP</span>
                                </div>
                                <div class="flex items-center justify-between py-2">
                                    <div class="flex items-center space-x-3">
                                        <svg class="w-4 h-4 text-dark-blue" fill="none" stroke="currentColor"
                                             viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                  d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                        </svg>
                                        <span class="text-dark-blue font-gotham-narrow text-[12px] font-[350]">Final chapter exam</span>
                                    </div>
                                    <span class="text-dark-blue font-gotham-narrow text-[12px] font-[350]">150 XP</span>
                                </div>
                            @endif
                        </div>
                    </div>
            </div>
            @endif
</div>
@endforeach
@endif
</div>
