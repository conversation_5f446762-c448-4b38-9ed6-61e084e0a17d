<!-- Total Engagement Section -->
<div class="bg-gray-f8 rounded-xl p-4">
    <!-- Header -->
    <div class="flex items-center gap-1 mb-4">
        <span class="text-dark-blue font-gotham-narrow text-[15px] font-[350] capitalize">Total Engagement</span>
        <svg class="w-[14px] h-[14px] text-light-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
    </div>
    
    <!-- Chart Area -->
    <div class="relative h-[212px] bg-white rounded-lg border border-gray-200 p-4">
    <!-- Y-axis labels -->
    <div class="absolute left-2 top-0 h-full flex flex-col justify-between text-gray-b2 font-gotham-narrow text-[10px] font-[350] py-4">
        <span>+4 Hr</span>
        <span>2 Hr</span>
        <span>60 Min</span>
        <span>30 Min</span>
        <span>0 Min</span>
    </div>

    <!-- Chart Container -->
    <div class="relative h-48 mb-4 ml-8">
        <canvas id="engagementChart" width="400" height="200"></canvas>
    </div>

    <!-- Stats -->
    <div class="text-center">
        <div class="text-dark-blue font-gotham-narrow text-[20px] font-[350]">{{ $totalHours }}h</div>
        <div class="text-gray-b2 font-gotham-narrow text-[12px] font-[350] capitalize">This Week</div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const ctx = document.getElementById('engagementChart').getContext('2d');
            
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: @json($chartData['labels']),
                    datasets: [{
                        data: @json($chartData['data']),
                        backgroundColor: function(context) {
                            const chart = context.chart;
                            const {ctx, chartArea} = chart;
                            
                            if (!chartArea) {
                                return null;
                            }
                            
                            // Create gradient
                            const gradient = ctx.createLinearGradient(0, chartArea.bottom, 0, chartArea.top);
                            gradient.addColorStop(0, '#007DB6');
                            gradient.addColorStop(1, '#102649');
                            
                            return gradient;
                        },
                        borderRadius: 4,
                        borderSkipped: false,
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            display: true,
                            grid: {
                                display: false
                            },
                            ticks: {
                                font: {
                                    size: 11
                                },
                                color: '#6B7280'
                            }
                        },
                        y: {
                            display: false,
                            grid: {
                                display: false
                            }
                        }
                    },
                    elements: {
                        bar: {
                            borderRadius: 4
                        }
                    }
                }
            });
        });
    </script>
</div>
