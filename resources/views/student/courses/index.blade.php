@extends('layouts.student')

@section('title', 'My Courses - BeaconLMS')
@section('page-title', 'My Courses')

@section('content')
<div class="space-y-6">
    <!-- Success/Error Messages -->
    @if(session('success'))
        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-green-800">{{ session('success') }}</p>
                </div>
            </div>
        </div>
    @endif

    @if(session('error'))
        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-red-800">{{ session('error') }}</p>
                </div>
            </div>
        </div>
    @endif

    <!-- Courses Header -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">My Courses</h1>
            <p class="text-gray-600 mt-1">Track your learning progress across all enrolled courses</p>
        </div>
        <div class="flex items-center space-x-3">
            <button onclick="window.open('{{ route('student.launch.moodle') }}', '_blank')"
                    class="bg-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-purple-700 transition-colors">
                🚀 Launch Moodle
            </button>
            <a href="{{ route('student.exam.update.form') }}"
               class="bg-green-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-green-700 transition-colors">
                📅 Update Exam Date
            </a>
        </div>
    </div>

    @if($enrollments && $enrollments->count() > 0)
        <!-- Courses Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            @foreach($enrollments as $enrollment)
                <div class="bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow">
                    <div class="p-6">
                        <!-- Course Header -->
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex-1">
                                <h3 class="font-semibold text-gray-900 text-lg leading-tight">
                                    {{ $enrollment->course->title ?? 'Course Title' }}
                                </h3>
                                @if($enrollment->course->description)
                                    <p class="text-sm text-gray-600 mt-1 line-clamp-2">
                                        {{ $enrollment->course->description }}
                                    </p>
                                @endif
                            </div>
                            <div class="ml-3">
                                @if(($enrollment->progress ?? 0) >= 100)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        ✅ Complete
                                    </span>
                                @else
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        📚 In Progress
                                    </span>
                                @endif
                            </div>
                        </div>

                        <!-- Progress Bar -->
                        <div class="mb-4">
                            <div class="flex justify-between text-sm mb-1">
                                <span class="text-gray-600">Progress</span>
                                <span class="font-medium">{{ $enrollment->progress ?? 0 }}%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                     style="width: {{ $enrollment->progress ?? 0 }}%"></div>
                            </div>
                        </div>

                        <!-- Course Stats -->
                        <div class="grid grid-cols-2 gap-4 mb-4">
                            <div class="text-center">
                                <div class="text-lg font-bold text-gray-900">{{ isset($enrollment->course->chapters) ? $enrollment->course->chapters->count() : 0 }}</div>
                                <div class="text-xs text-gray-500">Chapters</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-bold text-gray-900">{{ isset($enrollment->course->chapters) ? $enrollment->course->chapters->sum('items_count') : 0 }}</div>
                                <div class="text-xs text-gray-500">Activities</div>
                            </div>
                        </div>

                        <!-- Enrollment Info -->
                        <div class="text-xs text-gray-500 mb-4">
                            Enrolled {{ $enrollment->enrolled_at->format('M j, Y') }}
                        </div>

                        <!-- Action Buttons -->
                        <div class="space-y-2">
                            @if($enrollment->course->moodle_course_id)
                                <button onclick="window.open('{{ route('student.launch.course', $enrollment->course->moodle_course_id) }}', '_blank')"
                                        class="w-full bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors">
                                    Continue Learning
                                </button>
                            @else
                                <a href="{{ route('student.courses.show', $enrollment->id) }}"
                                   class="block w-full bg-blue-600 text-white text-center px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors">
                                    View Course
                                </a>
                            @endif

                            <button onclick="window.open('{{ route('student.profile') }}', '_blank')"
                                    class="w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-lg text-sm font-medium hover:bg-gray-200 transition-colors">
                                View Progress
                            </button>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>

        <!-- Summary Stats -->
        <div class="bg-white rounded-lg shadow-sm border">
            <div class="p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Learning Summary</h2>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600">{{ $enrollments->count() }}</div>
                        <div class="text-sm text-gray-500">Total Courses</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600">{{ $enrollments->where('progress', '>=', 100)->count() }}</div>
                        <div class="text-sm text-gray-500">Completed</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-orange-600">{{ $enrollments->avg('progress') ? round($enrollments->avg('progress')) : 0 }}%</div>
                        <div class="text-sm text-gray-500">Avg Progress</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-purple-600">{{ $enrollments->sum('progress') ? round($enrollments->sum('progress') / $enrollments->count()) : 0 }}%</div>
                        <div class="text-sm text-gray-500">Overall</div>
                    </div>
                </div>
            </div>
        </div>
    @else
        <!-- Empty State -->
        <div class="bg-white rounded-lg shadow-sm border">
            <div class="p-12 text-center">
                <div class="text-6xl mb-4">📚</div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No courses enrolled yet</h3>
                <p class="text-gray-600 mb-6">Start your learning journey by enrolling in your first course.</p>
                <div class="flex flex-col sm:flex-row gap-3 justify-center">
                    <button onclick="window.open('{{ route('student.launch.moodle') }}', '_blank')"
                            class="bg-purple-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-purple-700 transition-colors">
                        🚀 Launch Moodle
                    </button>
                    <a href="{{ route('student.dashboard') }}"
                       class="bg-gray-100 text-gray-700 px-6 py-3 rounded-lg font-medium hover:bg-gray-200 transition-colors">
                        Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    @endif
</div>

<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
@endsection
