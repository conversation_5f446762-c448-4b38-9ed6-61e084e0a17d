@extends('layouts.student')

@section('title', 'Student Dashboard - BeaconLMS')
@section('page-title', 'Member Dashboard')

@section('content')
<div class="space-y-3 font-gotham-narrow">
    <!-- Success/Error Messages -->
    @if(session('success'))
        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-green-800">{{ session('success') }}</p>
                </div>
            </div>
        </div>
    @endif

    @if(session('error'))
        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-red-800">{{ session('error') }}</p>
                </div>
            </div>
        </div>
    @endif
    <!-- Enrollment Banner -->
    <div class="text-dark-blue font-gotham-narrow text-[16px] font-[350] leading-normal tracking-[-0.16px]">
        You are enrolled in the
        <span class="font-[400] text-light-blue">CTP Certification</span>
        Track
    </div>

    <!-- Learn Section -->
    <div class="space-y-2">
        <div class="flex items-center gap-1">
            <svg class="w-[18px] h-[18px] text-dark-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
            </svg>
            <span class="text-dark-blue font-gotham-narrow text-[15px] font-[350] capitalize">LEARN</span>
        </div>

        <!-- Main Content Grid -->
        <div class="flex gap-6">
            <!-- Left Column - Course Content -->
            <div class="flex-1 space-y-2">
                <!-- Course Overview Card -->
                <div class="bg-gray-f8 rounded-xl p-5">
                    <div class="flex gap-6">
                        <div class="flex-1 space-y-4">
                            <!-- Course Header -->
                            <div class="flex items-center gap-2">
                                <span class="text-dark-blue font-gotham-narrow text-[14px] font-[350] tracking-[-0.14px]">
                                    INTERACTIVE STUDY PLAN
                                </span>
                                <div class="border border-dark-blue rounded px-4 py-1">
                                    <span class="text-dark-blue font-gotham-narrow text-[12px] font-[350]">
                                        Get a certificate upon completion
                                    </span>
                                </div>
                            </div>

                            <!-- Course Title -->
                            <h2 class="text-dark-blue font-gotham-narrow text-[20px] font-[350] tracking-[-0.2px]">
                                CTP Certification Exam Preparation
                            </h2>

                            <!-- Action Buttons -->
                            <div class="flex items-center gap-2">
                                <a href="{{ route('student.launch.moodle') }}" target="_blank" class="bg-yellow rounded px-4 py-2 text-dark-blue font-gotham-narrow text-[12px] font-[350] hover:bg-yellow/80 transition-colors inline-block">
                                    Continue Learning
                                </a>
                                <button class="bg-gray-5 rounded px-4 py-2 text-gray-b2 font-gotham-narrow text-[12px] font-[350] hover:bg-gray-5/80 transition-colors">
                                    Get Your Certification
                                </button>
                            </div>

                            <!-- Course Stats -->
                            <div class="flex flex-wrap items-center gap-3">
                                <div class="flex items-center gap-1">
                                    <div class="w-6 h-6 bg-white rounded-full flex items-center justify-center">
                                        <div class="w-3 h-3 bg-light-blue"></div>
                                    </div>
                                    <span class="text-dark-blue font-gotham-narrow text-[10px] font-[350]">Advanced</span>
                                </div>
                                <div class="flex items-center gap-1">
                                    <div class="w-6 h-6 bg-white rounded-full flex items-center justify-center">
                                        <svg class="w-3 h-3 text-light-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </div>
                                    <span class="text-dark-blue font-gotham-narrow text-[10px] font-[350]">{{ $stats['total_hours_required'] }} Hours</span>
                                </div>
                                <div class="flex items-center gap-1">
                                    <div class="w-6 h-6 bg-white rounded-full flex items-center justify-center">
                                        <svg class="w-3 h-3 text-light-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9 5a9 9 0 1118 0H3z"></path>
                                        </svg>
                                    </div>
                                    <span class="text-dark-blue font-gotham-narrow text-[10px] font-[350]">90 Videos</span>
                                </div>
                                <div class="flex items-center gap-1">
                                    <div class="w-6 h-6 bg-white rounded-full flex items-center justify-center">
                                        <svg class="w-3 h-3 text-light-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                        </svg>
                                    </div>
                                    <span class="text-dark-blue font-gotham-narrow text-[10px] font-[350]">30 Quizzes</span>
                                </div>
                                <div class="flex items-center gap-1">
                                    <div class="w-6 h-6 bg-white rounded-full flex items-center justify-center">
                                        <svg class="w-3 h-3 text-light-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                        </svg>
                                    </div>
                                    <span class="text-dark-blue font-gotham-narrow text-[10px] font-[350]">21070 Learners</span>
                                </div>
                                <div class="flex items-center gap-1">
                                    <div class="w-6 h-6 bg-white rounded-full flex items-center justify-center">
                                        <svg class="w-3 h-3 text-light-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                        </svg>
                                    </div>
                                    <span class="text-dark-blue font-gotham-narrow text-[10px] font-[350]">Updated last Aug 2023</span>
                                </div>
                                <div class="bg-light-blue rounded px-4 py-1">
                                    <span class="text-gray-ee font-gotham-narrow text-[10px] font-[350]">{{ $stats['total_xp'] }} XP</span>
                                </div>
                            </div>
                        </div>

                        <!-- Course Image -->
                        <div class="w-[135px] h-[60px]">
                            <img
                                src="https://api.builder.io/api/v1/image/assets/TEMP/92af737f290e8b6cb3702271df12e393751366a7?width=270"
                                alt="CTP Certification"
                                class="w-full h-full object-cover rounded-lg"
                            />
                        </div>
                    </div>
                </div>

                <!-- Chapter List -->
                @livewire('student.chapter-list')

                <!-- Explore More Section -->
                <div class="space-y-2 mt-8">
                    <span class="text-dark-blue font-gotham-narrow text-[15px] font-[350]">Explore More</span>

                    <div class="bg-gray-f8 rounded-xl p-3">
                        <img
                            src="https://api.builder.io/api/v1/image/assets/TEMP/a1a257cba01948cc9974fb95363e34ee3bd542a0?width=334"
                            alt="Future of Finance"
                            class="w-[167px] h-[65px] object-contain mb-3"
                        />
                        <div class="flex items-center justify-between">
                            <span class="text-dark-blue font-gotham-narrow text-[14px] font-[350] tracking-[-0.14px]">
                                Connect, learn, and grow at Future of Finance MEA | Cairo 2025
                            </span>
                            <button class="bg-dark-blue text-gray-ee rounded px-4 py-2 font-gotham-narrow text-[12px] font-[350] hover:bg-dark-blue/80 transition-colors">
                                Register now
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Profile Section -->
            <div class="w-[361px] flex flex-col gap-2">
                <!-- Profile Card -->
                <div class="bg-gray-f8 rounded-xl p-3">
                    <div class="flex items-start gap-3">
                        <img
                            src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
                            alt="Profile"
                            class="w-[63px] h-[63px] rounded-lg object-cover"
                        />
                        <div class="flex-1 space-y-1">
                            <div class="text-dark-blue font-gotham-narrow text-[12px] font-[325] capitalize">
                                Welcome,
                            </div>
                            <div class="text-light-blue font-gotham-narrow text-[12px] font-[350] capitalize">
                                {{ Auth::user()->first_name ?? 'Student' }}
                            </div>
                            <div class="flex items-center justify-between text-dark-blue font-gotham-narrow text-[10px] font-[325] capitalize cursor-pointer hover:text-light-blue">
                                <span>complete your profile</span>
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </div>
                            <div class="flex items-center gap-2">
                                <div class="flex-1 h-[5px] bg-white rounded-full relative">
                                    <div class="w-[30px] h-[3px] bg-yellow rounded-full absolute top-[1px] left-[1px]"></div>
                                </div>
                                <span class="text-gray-b2 font-gotham-narrow text-[10px] font-[350] tracking-[-0.1px]">25%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Stats Cards Row -->
                <div class="grid grid-cols-2 gap-1">
                    <!-- Daily Streak -->
                    <div class="bg-gray-f8 rounded-xl p-3">
                        <div class="flex items-center gap-1 mb-2">
                            <span class="text-dark-blue font-gotham-narrow text-[12px] font-[350] capitalize">Daily Streak</span>
                            <svg class="w-3 h-3 text-light-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="w-5 h-5 bg-white border border-yellow rounded-full flex items-center justify-center">
                                <svg class="w-3 h-3 text-yellow fill-yellow" fill="currentColor" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>
                            </div>
                            <span class="text-dark-blue font-gotham-narrow text-[12px] font-[350] capitalize">{{ $stats['current_streak'] ?? 2 }} Days</span>
                        </div>
                    </div>

                    <!-- CTP Exam -->
                    <div class="bg-gray-f8 rounded-xl p-3">
                        <div class="flex items-center gap-1 mb-2">
                            <span class="text-dark-blue font-gotham-narrow text-[12px] font-[350] capitalize">Your CTP Exam</span>
                            <svg class="w-3 h-3 text-light-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="space-y-1">
                            <div class="flex items-baseline gap-1">
                                <span class="text-dark-blue font-gotham-narrow text-[20px] font-[350]">{{ $stats['days_left_exam'] ?? 43 }}</span>
                                <span class="text-dark-blue font-gotham-narrow text-[12px] font-[350] capitalize"> Days Left</span>
                            </div>
                            <div class="text-light-blue font-gotham-narrow text-[10px] font-[325] underline capitalize cursor-pointer hover:text-light-blue/80">
                                <a class="text-light-blue font-gotham-narrow text-[10px] font-[325] underline capitalize cursor-pointer hover:text-light-blue/80" href="{{route('student.exam.update.form')}}">Update the date</a>
                            </div>
                        </div>
                    </div>
                </div>


                <!-- Enrolled Track -->
                <div class="bg-gray-f8 rounded-xl p-4">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center gap-1">
                            <span class="text-dark-blue font-gotham-narrow text-[15px] font-[350] capitalize">Enrolled Track</span>
                            <svg class="w-4 h-4 text-dark-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </div>
                        <div class="bg-gray-ee rounded px-4 py-1">
                            <span class="text-light-blue font-gotham-narrow text-[10px] font-[350] underline cursor-pointer hover:text-light-blue/80">View Details</span>
                        </div>
                    </div>

                    <div class="space-y-3">
                        <div class="text-light-blue font-gotham-narrow text-[14px] font-[350] capitalize">
                            CTP Certification Preparation
                        </div>
                        <div class="space-y-1">
                            <div class="w-full h-[5px] bg-white rounded-full relative">
                                <div class="bg-yellow rounded-full h-[5px]" style="width: {{ $stats['progress_percentage'] ?? 0 }}%"></div>
                            </div>
                            <span class="text-dark-blue font-gotham-narrow text-[10px] font-[350] capitalize">
                                {{ (int) ($stats['hours_completed'] ?? 90) }}/{{ (int) ($stats['total_hours_required'] ?? 120) }} Hours to complete
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Total Engagement Chart -->
                @livewire('student.engagement-chart')
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="{{ asset('js/dashboard.js') }}"></script>
@endpush
