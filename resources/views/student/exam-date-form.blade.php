@extends('layouts.student')

@section('title', 'Update Exam Date - BeaconLMS')
@section('page-title', 'Update Exam Date')

@section('content')
<div class="max-w-2xl mx-auto space-y-6">
    <!-- Success/Error Messages -->
    @if(session('success'))
        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-green-800">{{ session('success') }}</p>
                </div>
            </div>
        </div>
    @endif

    @if(session('error'))
        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-red-800">{{ session('error') }}</p>
                </div>
            </div>
        </div>
    @endif

    <!-- Exam Date Form -->
    <div class="bg-white rounded-lg shadow-sm border">
        <div class="p-6">
            <div class="flex items-center justify-between mb-6">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Update Exam Date</h1>
                    <p class="text-gray-600 mt-1">Set your CTP Certification exam date to track your preparation progress</p>
                </div>
                <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                </div>
            </div>

            <form action="{{ route('student.exam.update') }}" method="POST" class="space-y-6">
                @csrf

                <!-- Current Exam Date Info -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0">
                            <svg class="w-5 h-5 text-blue-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-blue-900">Current Exam Date</h3>
                            <p class="text-sm text-blue-700 mt-1">
                                @if($enrollment && $enrollment->exam_date)
                                    Your exam is scheduled for <strong>{{ $enrollment->exam_date->format('l, F j, Y') }}</strong>
                                    ({{ (int) now()->diffInDays($enrollment->exam_date, false) }} days remaining)
                                @else
                                    No exam date set. Using default preparation period of {{ $defaultDays }} days.
                                @endif
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Exam Date Selection -->
                <div>
                    <label for="exam_date" class="block text-sm font-medium text-gray-700 mb-2">
                        Select Your Exam Date *
                    </label>
                    <div class="relative">
                        <input type="date"
                               id="exam_date"
                               name="exam_date"
                               value="{{ $currentExamDate ? $currentExamDate->format('Y-m-d') : now()->addDays($defaultDays)->format('Y-m-d') }}"
                               min="{{ now()->addDay()->format('Y-m-d') }}"
                               required
                               class="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 @error('exam_date') border-red-300 @enderror">

                        @error('exam_date')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                    <p class="mt-1 text-sm text-gray-500">
                        Choose a date in the future. We recommend scheduling your exam after completing your preparation.
                    </p>
                </div>

                <!-- Preparation Timeline -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <h3 class="text-sm font-medium text-gray-900 mb-3">Recommended Preparation Timeline</h3>
                    <div class="space-y-2 text-sm text-gray-600">
                        <div class="flex justify-between">
                            <span>Minimum preparation time:</span>
                            <span class="font-medium">{{ config('beaconlms.business_rules.minimum_preparation_days', 30) }} days</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Default preparation period:</span>
                            <span class="font-medium">{{ $defaultDays }} days</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Maximum reschedules allowed:</span>
                            <span class="font-medium">{{ config('beaconlms.business_rules.max_exam_reschedules', 3) }} times</span>
                        </div>
                    </div>
                </div>

                <!-- Quick Date Options -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-3">Quick Select</label>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
                        @php
                            $quickDates = [
                                30 => '1 Month',
                                45 => '6 Weeks',
                                60 => '2 Months',
                                90 => '3 Months'
                            ];
                        @endphp
                        @foreach($quickDates as $days => $label)
                            <button type="button"
                                    onclick="setExamDate({{ $days }})"
                                    class="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                                {{ $label }}
                            </button>
                        @endforeach
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-col sm:flex-row gap-3 pt-4">
                    <button type="submit"
                            class="flex-1 bg-blue-600 text-white px-6 py-3 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors">
                        Update Exam Date
                    </button>
                    <a href="{{ route('student.dashboard') }}"
                       class="flex-1 bg-gray-100 text-gray-700 px-6 py-3 rounded-lg text-sm font-medium hover:bg-gray-200 transition-colors text-center">
                        Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Progress Preview -->
    @if($currentExamDate)
        <div class="bg-white rounded-lg shadow-sm border">
            <div class="p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Progress Preview</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600">{{ (int) now()->diffInDays($currentExamDate, false) }}</div>
                        <div class="text-sm text-gray-500">Days Remaining</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600">{{ (int) ceil(now()->diffInDays($currentExamDate, false) / 7) }}</div>
                        <div class="text-sm text-gray-500">Weeks Left</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-purple-600">{{ (int) ceil(now()->diffInDays($currentExamDate, false) / 30) }}</div>
                        <div class="text-sm text-gray-500">Months Left</div>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>

<script>
function setExamDate(days) {
    const today = new Date();
    const examDate = new Date(today);
    examDate.setDate(today.getDate() + days);

    // Format date as YYYY-MM-DD for input
    const formattedDate = examDate.toISOString().split('T')[0];
    document.getElementById('exam_date').value = formattedDate;
}
</script>
@endsection
