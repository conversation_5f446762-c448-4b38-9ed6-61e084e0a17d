@extends('layouts.student')

@section('title', 'Student Profile - BeaconLMS')
@section('page-title', 'My Profile')

@section('content')
<div class="space-y-6">
    <!-- Success/Error Messages -->
    @if(session('success'))
        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-green-800">{{ session('success') }}</p>
                </div>
            </div>
        </div>
    @endif

    @if(session('error'))
        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-red-800">{{ session('error') }}</p>
                </div>
            </div>
        </div>
    @endif

    <!-- Profile Header -->
    <div class="bg-white rounded-lg shadow-sm border">
        <div class="p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center">
                        <span class="text-white font-bold text-xl">{{ substr(Auth::user()->name, 0, 1) }}</span>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">{{ Auth::user()->name }}</h1>
                        <p class="text-gray-600">{{ Auth::user()->email }}</p>
                        <div class="flex items-center space-x-2 mt-1">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                Student
                            </span>
                            <span class="text-sm text-gray-500">Member since {{ Auth::user()->created_at->format('M Y') }}</span>
                        </div>
                    </div>
                </div>
                <div class="text-right">
                    <div class="text-sm text-gray-500">Current Level</div>
                    <div class="text-2xl font-bold text-blue-600">{{ floor(($stats['total_xp'] ?? 0) / 100) + 1 }}</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Left Column - Stats & Progress -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Stats Overview -->
            <div class="bg-white rounded-lg shadow-sm border">
                <div class="p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">Performance Overview</h2>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-blue-600">{{ is_array($stats['total_xp']) ? implode(', ', $stats['total_xp']) : ($stats['total_xp'] ?? 0) }}</div>
                            <div class="text-sm text-gray-500">Total XP</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-green-600">{{ is_array($stats['current_streak']) ? implode(', ', $stats['current_streak']) : ($stats['current_streak'] ?? 0) }}</div>
                            <div class="text-sm text-gray-500">Day Streak</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-purple-600">{{ (int) (is_array($stats['total_study_hours']) ? implode(', ', $stats['total_study_hours']) : ($stats['total_study_hours'] ?? 0)) }}</div>
                            <div class="text-sm text-gray-500">Study Hours</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-orange-600">{{ is_array($stats['courses_completed']) ? implode(', ', $stats['courses_completed']) : ($stats['courses_completed'] ?? 0) }}/{{ is_array($stats['courses_enrolled']) ? implode(', ', $stats['courses_enrolled']) : ($stats['courses_enrolled'] ?? 0) }}</div>
                            <div class="text-sm text-gray-500">Courses Done</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Progress Chart -->
            <div class="bg-white rounded-lg shadow-sm border">
                <div class="p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">Study Activity (Last 30 Days)</h2>
                    <div class="h-64 flex items-end justify-between space-x-2">
                        @php
                            $maxHours = is_array($streakData) ? collect($streakData)->max() : 1;
                            $maxHours = $maxHours ?: 1;
                        @endphp
                        @for($i = 0; $i < 30; $i++)
                            @php
                                $date = now()->subDays(29 - $i);
                                $dateKey = $date->format('Y-m-d');
                                $hours = is_array($streakData) && isset($streakData[$dateKey]) ? $streakData[$dateKey] : 0;
                                $height = $maxHours > 0 ? ($hours / $maxHours) * 100 : 0;
                            @endphp
                            <div class="flex flex-col items-center flex-1">
                                <div class="w-full bg-gray-200 rounded-t" style="height: {{ 200 * ($height / 100) }}px; background-color: {{ $hours > 0 ? '#3B82F6' : '#E5E7EB' }};"></div>
                                <div class="text-xs text-gray-500 mt-2">{{ $date->format('d') }}</div>
                            </div>
                        @endfor
                    </div>
                </div>
            </div>

            <!-- Current Progress -->
            <div class="bg-white rounded-lg shadow-sm border">
                <div class="p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">Current Progress</h2>
                    <div class="space-y-4">
                        <div>
                            <div class="flex justify-between text-sm mb-1">
                                <span class="text-gray-600">Average Progress</span>
                                <span class="font-medium">{{ is_array($stats['average_progress']) ? implode(', ', $stats['average_progress']) : ($stats['average_progress'] ?? 0) }}%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-blue-600 h-2 rounded-full" style="width: {{ is_array($stats['average_progress']) ? implode(', ', $stats['average_progress']) : ($stats['average_progress'] ?? 0) }}%"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex justify-between text-sm mb-1">
                                <span class="text-gray-600">Activities Completed</span>
                                <span class="font-medium">{{ is_array($stats['total_activities_completed']) ? implode(', ', $stats['total_activities_completed']) : ($stats['total_activities_completed'] ?? 0) }}</span>
                            </div>
                        </div>
                        <div>
                            <div class="flex justify-between text-sm mb-1">
                                <span class="text-gray-600">Last Synced</span>
                                <span class="font-medium">{{ is_array($stats['last_synced']) ? implode(', ', $stats['last_synced']) : ($stats['last_synced'] ?? 'Never') }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Column - Achievements -->
        <div class="space-y-6">
            <!-- Achievements -->
            <div class="bg-white rounded-lg shadow-sm border">
                <div class="p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">Achievements</h2>
                    @if(!empty($achievements))
                        <div class="space-y-3">
                            @foreach($achievements as $achievement)
                                <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                                    <div class="text-2xl">{{ $achievement['icon'] ?? '🏆' }}</div>
                                    <div class="flex-1">
                                        <div class="font-medium text-gray-900">{{ is_array($achievement['name']) ? implode(', ', $achievement['name']) : $achievement['name'] }}</div>
                                        <div class="text-sm text-gray-600">{{ is_array($achievement['description']) ? implode(', ', $achievement['description']) : $achievement['description'] }}</div>
                                        <div class="text-xs text-gray-500">Earned {{ is_string($achievement['earned_at']) ? $achievement['earned_at'] : (is_object($achievement['earned_at']) ? $achievement['earned_at']->diffForHumans() : 'Recently') }}</div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-8">
                            <div class="text-4xl mb-2">🎯</div>
                            <div class="text-gray-500">No achievements yet</div>
                            <div class="text-sm text-gray-400">Complete lessons and activities to earn achievements!</div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow-sm border">
                <div class="p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
                    <div class="space-y-3">
                        <a href="{{ route('student.dashboard') }}"
                           class="block w-full bg-blue-600 text-white text-center px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors">
                            Back to Dashboard
                        </a>
                        <a href="{{ route('student.courses.index') }}"
                           class="block w-full bg-gray-100 text-gray-700 text-center px-4 py-2 rounded-lg text-sm font-medium hover:bg-gray-200 transition-colors">
                            My Courses
                        </a>
                        <a href="{{ route('student.exam.update.form') }}"
                           class="block w-full bg-green-600 text-white text-center px-4 py-2 rounded-lg text-sm font-medium hover:bg-green-700 transition-colors">
                            Update Exam Date
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
