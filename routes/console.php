<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

// Sync all students progress command (for manual sync)
Artisan::command('moodle:sync-all', function () {
    $this->comment('Syncing all student progress from Moodle...');

    $students = \App\Models\User::where('role', 'student')
                                ->whereNotNull('moodle_user_id')
                                ->get();

    $this->info("Found {$students->count()} students to sync.");

    foreach ($students as $student) {
        \App\Jobs\SyncStudentProgress::dispatch($student);
        $this->line("Dispatched sync job for: {$student->email}");
    }

    $this->info('All sync jobs have been dispatched.');
})->purpose('Manually sync all student progress from Moodle');

// Sync specific student progress
Artisan::command('moodle:sync-student {email}', function ($email) {
    $this->comment("Syncing progress for student: {$email}");

    $student = \App\Models\User::where('email', $email)
                               ->where('role', 'student')
                               ->first();

    if (!$student) {
        $this->error("Student not found: {$email}");
        return 1;
    }

    if (!$student->moodle_user_id) {
        $this->error("Student has no Moodle user ID: {$email}");
        return 1;
    }

    \App\Jobs\SyncStudentProgress::dispatch($student);
    $this->info("Sync job dispatched for: {$student->email}");
})->purpose('Sync progress for a specific student');

// Test Moodle connection command
Artisan::command('moodle:test', function () {
    $this->comment('Testing Moodle connection...');

    try {
        $moodleService = app(\App\Services\MoodleService::class);
        $siteInfo = $moodleService->testConnection();

        $this->info('✅ Moodle connection successful!');
        $this->line("Site Name: {$siteInfo['sitename']}");
        $this->line("Release: {$siteInfo['release']}");
        $this->line("Version: {$siteInfo['version']}");

    } catch (\Exception $e) {
        $this->error('❌ Moodle connection failed: ' . $e->getMessage());
        return 1;
    }
})->purpose('Test the connection to Moodle API');

// Show sync status for all students
Artisan::command('moodle:sync-status', function () {
    $this->comment('Student Progress Sync Status');
    $this->line('');

    $students = \App\Models\User::where('role', 'student')
                                ->whereNotNull('moodle_user_id')
                                ->get();

    $headers = ['Email', 'Moodle ID', 'Last Synced', 'Enrollments', 'Avg Progress'];
    $rows = [];

    foreach ($students as $student) {
        $enrollments = $student->courseEnrollments;
        $avgProgress = $enrollments->avg('progress') ?? 0;
        
        $rows[] = [
            $student->email,
            $student->moodle_user_id,
            $student->last_synced_at ? $student->last_synced_at->diffForHumans() : 'Never',
            $enrollments->count(),
            round($avgProgress, 1) . '%'
        ];
    }

    $this->table($headers, $rows);
})->purpose('Show sync status for all students');
