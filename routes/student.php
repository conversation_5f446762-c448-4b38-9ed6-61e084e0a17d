<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\StudentDashboardController;

// Student Dashboard Routes - Protected by role-based middleware
Route::middleware(['auth', 'role:student'])->prefix('student')->name('student.')->group(function () {
    Route::get('/dashboard', [StudentDashboardController::class, 'dashboard'])->name('dashboard');
    Route::get('/courses', [StudentDashboardController::class, 'courses'])->name('courses.index');
    Route::get('/courses/{id}', [StudentDashboardController::class, 'course'])->name('courses.show');
    Route::get('/courses/{courseId}/chapters/{chapterId}', [StudentDashboardController::class, 'chapter'])->name('chapters.show');
    Route::post('/courses/{courseId}/chapters/{chapterId}/items/{itemId}/complete', [StudentDashboardController::class, 'completeItem'])->name('items.complete');
    Route::get('/profile', [StudentDashboardController::class, 'profile'])->name('profile');

    // SSO Routes - Extra security for Moodle access
    Route::get('/launch-moodle', [StudentDashboardController::class, 'launchMoodle'])->name('launch.moodle');
    Route::get('/launch-course/{courseId?}', [StudentDashboardController::class, 'launchMoodleCourse'])->name('launch.course');

    // Exam Date Management
    Route::get('/exam/update', [StudentDashboardController::class, 'showExamDateForm'])->name('exam.update.form');
    Route::post('/exam/update', [StudentDashboardController::class, 'updateExamDate'])->name('exam.update');
});
